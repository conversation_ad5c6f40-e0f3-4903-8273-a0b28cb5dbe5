package com.tipray.transaction.at.context;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AT配置上下文
 * 用于在不同模块间传递AT服务配置信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AtConfigContext {

    /**
     * 线程本地变量，存储当前AT配置
     */
    private static final ThreadLocal<AtConfigContext> CONTEXT_HOLDER = new ThreadLocal<>();
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 服务描述
     */
    private String description;
    /**
     * 连接超时时间（毫秒）
     */
    private long connectTimeout;
    /**
     * 读取超时时间（毫秒）
     */
    private long readTimeout;
    /**
     * 执行超时时间（毫秒）
     */
    private long timeout;
    /**
     * 重试次数
     */
    private int retryCount;
    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval;
    /**
     * 是否为关键服务
     */
    private boolean critical;

    /**
     * 获取当前线程的AT配置
     *
     * @return AT配置上下文，如果没有设置则返回null
     */
    public static AtConfigContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 设置当前线程的AT配置
     *
     * @param context AT配置上下文
     */
    public static void setContext(AtConfigContext context) {
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 清除当前线程的AT配置
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 创建AT配置上下文
     *
     * @param serviceName    服务名称
     * @param description    服务描述
     * @param connectTimeout 连接超时时间
     * @param readTimeout    读取超时时间
     * @param retryCount     重试次数
     * @param retryInterval  重试间隔
     * @param critical       是否为关键服务
     * @return AT配置上下文
     */
    public static AtConfigContext create(String serviceName, String description,
                                         long connectTimeout, long readTimeout,
                                         int retryCount, long retryInterval, boolean critical) {
        AtConfigContext context = new AtConfigContext();
        context.serviceName = serviceName;
        context.description = description;
        context.connectTimeout = connectTimeout;
        context.readTimeout = readTimeout;
        context.retryCount = retryCount;
        context.retryInterval = retryInterval;
        context.critical = critical;
        return context;
    }

    /**
     * 创建AT配置上下文（包含执行超时）
     *
     * @param serviceName    服务名称
     * @param description    服务描述
     * @param timeout        执行超时时间（毫秒）
     * @param connectTimeout 连接超时时间
     * @param readTimeout    读取超时时间
     * @param retryCount     重试次数
     * @param retryInterval  重试间隔
     * @param critical       是否为关键服务
     * @return AT配置上下文
     */
    public static AtConfigContext create(String serviceName, String description,
                                         long timeout, long connectTimeout, long readTimeout,
                                         int retryCount, long retryInterval, boolean critical) {
        AtConfigContext context = new AtConfigContext();
        context.serviceName = serviceName;
        context.description = description;
        context.timeout = timeout;
        context.connectTimeout = connectTimeout;
        context.readTimeout = readTimeout;
        context.retryCount = retryCount;
        context.retryInterval = retryInterval;
        context.critical = critical;
        return context;
    }

    /**
     * 获取连接超时时间，如果没有配置则返回默认值
     *
     * @param defaultValue 默认值
     * @return 连接超时时间
     */
    public long getConnectTimeoutOrDefault(long defaultValue) {
        return connectTimeout > 0 ? connectTimeout : defaultValue;
    }

    /**
     * 获取读取超时时间，如果没有配置则返回默认值
     *
     * @param defaultValue 默认值
     * @return 读取超时时间
     */
    public long getReadTimeoutOrDefault(long defaultValue) {
        return readTimeout > 0 ? readTimeout : defaultValue;
    }

    /**
     * 获取重试次数，如果没有配置则返回默认值
     *
     * @param defaultValue 默认值
     * @return 重试次数
     */
    public int getRetryCountOrDefault(int defaultValue) {
        return retryCount >= 0 ? retryCount : defaultValue;
    }

    /**
     * 获取重试间隔，如果没有配置则返回默认值
     *
     * @param defaultValue 默认值
     * @return 重试间隔
     */
    public long getRetryIntervalOrDefault(long defaultValue) {
        return retryInterval > 0 ? retryInterval : defaultValue;
    }

    /**
     * 获取执行超时时间（毫秒）
     *
     * @return 执行超时时间
     */
    public long getTimeout() {
        return timeout;
    }

    /**
     * 获取执行超时时间，如果没有配置则返回默认值
     *
     * @param defaultValue 默认值（毫秒）
     * @return 执行超时时间
     */
    public long getTimeoutOrDefault(long defaultValue) {
        return timeout > 0 ? timeout : defaultValue;
    }

    @Override
    public String toString() {
        return String.format("AtConfigContext{serviceName='%s', description='%s', " +
                        "connectTimeout=%d, readTimeout=%d, retryCount=%d, retryInterval=%d, critical=%s}",
                serviceName, description, connectTimeout, readTimeout,
                retryCount, retryInterval, critical);
    }
}
