package com.tipray.transaction.at.handler;

import com.tipray.transaction.at.context.AtConfigContext;
import com.tipray.transaction.at.http.security.SecureTransactionClient;
import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import com.tipray.transaction.core.application.handler.mode.AbstractTransactionModeHandler;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.BranchTransationConfig;
import com.tipray.transaction.core.enums.TransactionMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;

/**
 * AT模式事务处理器
 * 继承AbstractTransactionHandler，实现AT模式特定的事务处理逻辑
 * 负责AT模式的UndoLog管理和云服务通信
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Slf4j
public class AtTransactionModeHandler extends AbstractTransactionModeHandler {

    @Autowired
    private SecureTransactionClient secureTransactionClient;

    @Override
    public TransactionMode getSupportedMode() {
        return TransactionMode.AT;
    }

    @Override
    public String getHandlerName() {
        return "AtTransactionModeHandler";
    }


    @Override
    public BranchTransationConfig parseBranchTransationConfig(DistributedBranchTransaction annotation, Method method, TransactionMode mode) {
        BranchTransationConfig config = super.parseBranchTransationConfig(annotation, method, mode);

        // 校验AT模式必须参数
        validateAtModeRequiredParameters(annotation, method);

        // 设置AT上下文
        setupAtConfigContext(config);
        return config;
    }

    /**
     * 校验AT模式必须参数
     *
     * @param annotation 分支事务注解
     * @param method     方法
     */
    private void validateAtModeRequiredParameters(DistributedBranchTransaction annotation, Method method) {
        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();

        // 1. 目标服务名称（必须）
        if (annotation.targetService() == null || annotation.targetService().trim().isEmpty()) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务必须指定目标服务 (targetService): %s", methodName));
        }

        // 3. 超时时间校验（必须大于0）
        if (annotation.timeout() <= 0) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务超时时间必须大于0: %s, 当前值: %d", methodName, annotation.timeout()));
        }

        // 4. 连接超时时间校验（必须大于0）
        if (annotation.connectTimeout() <= 0) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务连接超时时间必须大于0: %s, 当前值: %d", methodName, annotation.connectTimeout()));
        }

        // 5. 读取超时时间校验（必须大于0）
        if (annotation.readTimeout() <= 0) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务读取超时时间必须大于0: %s, 当前值: %d", methodName, annotation.readTimeout()));
        }

        // 6. 重试次数校验（不能为负数）
        if (annotation.retryCount() < 0) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务重试次数不能为负数: %s, 当前值: %d", methodName, annotation.retryCount()));
        }

        // 7. 重试间隔校验（如果有重试，间隔必须大于0）
        if (annotation.retryCount() > 0 && annotation.retryInterval() <= 0) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务设置了重试次数时，重试间隔必须大于0: %s, 重试次数: %d, 重试间隔: %d",
                    methodName, annotation.retryCount(), annotation.retryInterval()));
        }

        // 10. 步骤名称校验（如果指定了value，不能为空）
        if (!annotation.value().isEmpty() && annotation.value().trim().isEmpty()) {
            throw new IllegalArgumentException(String.format(
                    "AT模式分支事务步骤名称不能为空字符串: %s", methodName));
        }

        log.debug("AT模式分支事务参数校验通过: {} -> {}", methodName, annotation.targetService());
    }

    @Override
    public void clearBranchTransactionResource() {
        super.clearBranchTransactionResource();
        // 清理at上下文
        AtConfigContext.clearContext();
    }

    /**
     * 构建回滚数据
     */
    private Object buildRollbackData(BranchTransactionDO branchTransactionDO) {
        return new Object() {
            public final String transactionId = branchTransactionDO.getTransactionId();
            public final Long branchId = branchTransactionDO.getBranchTransactionId();
            public final Long stepId = branchTransactionDO.getBranchTransactionId();
            public final String stepName = branchTransactionDO.getStepName();
            public final String targetService = branchTransactionDO.getTargetService();
            public final String action = "rollback";
        };
    }

    /**
     * 构建提交数据
     */
    private Object buildCommitData(BranchTransactionDO branchTransactionDO) {
        return new Object() {
            public final String transactionId = branchTransactionDO.getTransactionId();
            public final Long branchId = branchTransactionDO.getBranchTransactionId();
            public final Long stepId = branchTransactionDO.getBranchTransactionId();
            public final String stepName = branchTransactionDO.getStepName();
            public final String targetService = branchTransactionDO.getTargetService();
            public final String action = "commit";
        };
    }

    @Override
    protected void doBranchRollback(BranchTransactionDO branchTransactionDO, Exception cause) {
        String transactionId = branchTransactionDO.getTransactionId();
        Long branchId = branchTransactionDO.getBranchTransactionId();

        logger.debug("[{}] - [AT] 回滚分支事务: 服务[{}], 分支ID[{}]",
                transactionId, branchTransactionDO.getTargetService(), branchId);

        // 构建回滚数据
        Object rollbackData = buildRollbackData(branchTransactionDO);

        // 使用SecureTransactionClient发送安全的回滚请求
        SecureTransactionClient.TransactionResponse response = secureTransactionClient.sendRollbackRequest(
                branchTransactionDO.getTargetService(),
                transactionId,
                branchId,
                rollbackData,
                branchTransactionDO.isEnableBarrier()
        );

        if (response.isSuccess()) {
            logger.info("[{}] - [AT] 分支事务回滚成功: 服务[{}], 响应[{}]",
                    transactionId, branchTransactionDO.getTargetService(), response.getResponseBody());
        } else {
            throw new RuntimeException("回滚请求失败: " + response.getMessage());
        }
    }

    @Override
    protected void doBranchCommit(BranchTransactionDO branchTransactionDO) {

        String transactionId = branchTransactionDO.getTransactionId();
        Long branchId = branchTransactionDO.getBranchTransactionId();

        // 构建回滚数据
        Object commitData = buildCommitData(branchTransactionDO);

        SecureTransactionClient.TransactionResponse response = secureTransactionClient.sendCommitRequest(
                branchTransactionDO.getTargetService(), transactionId, branchId, commitData, branchTransactionDO.isEnableBarrier());

        if (response.isSuccess()) {
            logger.info("[{}] - [AT] 分支事务提交成功: 服务[{}], 响应[{}]",
                    transactionId, branchTransactionDO.getTargetService(), response.getResponseBody());
        } else {
            logger.warn("[{}] - [AT] 分支事务提交失败: 服务[{}], 响应[{}]",
                    transactionId, branchTransactionDO.getTargetService(), response.getResponseBody());
        }
    }

    /**
     * 获取简化的错误信息，避免日志刷屏
     */
    private String getSimpleErrorMessage(Throwable throwable) {
        if (throwable == null) {
            return "未知错误";
        }

        String message = throwable.getMessage();
        if (message == null || message.trim().isEmpty()) {
            return throwable.getClass().getSimpleName();
        }

        // 限制错误信息长度，避免过长的日志
        if (message.length() > 150) {
            return message.substring(0, 150) + "...";
        }

        return message;
    }

    /**
     * 为AT模式设置配置上下文
     * 将注解中的配置信息设置到AtConfigContext中，供AtHttpClient使用
     *
     * @param config 步骤配置
     */
    private void setupAtConfigContext(BranchTransationConfig config) {
        AtConfigContext atConfig = AtConfigContext.create(
                config.getTargetService(),           // 服务名称
                config.getDescription(),             // 服务描述
                config.getTimeout(),                 // 执行超时时间（毫秒）
                config.getConnectTimeout(),          // 连接超时时间（毫秒）
                config.getReadTimeout(),             // 读取超时时间（毫秒）
                config.getRetryCount(),              // 重试次数
                config.getRetryInterval(),           // 重试间隔（毫秒）
                config.isCritical()                 // 是否为关键服务
        );

        AtConfigContext.setContext(atConfig);
    }


}
