package com.tipray.transaction.core.domain.propagation;

import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionPropagation;

import java.util.HashMap;
import java.util.Map;

/**
 * 事务传播上下文
 * 包含传播处理所需的信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionPropagationContext {

    private final String groupId;
    private final TransactionMode mode;
    private final TransactionPropagation propagation;
    private final String transactionName;
    private final int timeout;
    private final boolean readOnly;
    private final String initiator;
    private final Class<? extends Throwable>[] rollbackFor;
    private final Class<? extends Throwable>[] noRollbackFor;
    private final Map<String, Object> attributes;

    private TransactionPropagationContext(Builder builder) {
        this.groupId = builder.groupId;
        this.mode = builder.mode;
        this.propagation = builder.propagation;
        this.transactionName = builder.transactionName;
        this.timeout = builder.timeout;
        this.readOnly = builder.readOnly;
        this.initiator = builder.initiator;
        this.rollbackFor = builder.rollbackFor.clone();
        this.noRollbackFor = builder.noRollbackFor.clone();
        this.attributes = new HashMap<>(builder.attributes);
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 判断是否需要为指定异常回滚
     */
    public boolean shouldRollbackFor(Throwable throwable) {
        Class<?> throwableClass = throwable.getClass();

        // 检查noRollbackFor规则
        for (Class<? extends Throwable> noRollbackClass : noRollbackFor) {
            if (noRollbackClass.isAssignableFrom(throwableClass)) {
                return false;
            }
        }

        // 检查rollbackFor规则
        for (Class<? extends Throwable> rollbackClass : rollbackFor) {
            if (rollbackClass.isAssignableFrom(throwableClass)) {
                return true;
            }
        }

        // 默认规则：RuntimeException和Error需要回滚
        return throwable instanceof RuntimeException || throwable instanceof Error;
    }

    /**
     * 获取有效的超时时间（毫秒）
     */
    public long getEffectiveTimeoutMillis() {
        return timeout > 0 ? timeout : Long.MAX_VALUE;
    }

    /**
     * 添加属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    /**
     * 创建副本
     */
    public TransactionPropagationContext copy() {
        return builder()
                .groupId(groupId)
                .mode(mode)
                .propagation(propagation)
                .transactionName(transactionName)
                .timeout(timeout)
                .readOnly(readOnly)
                .initiator(initiator)
                .rollbackFor(rollbackFor)
                .noRollbackFor(noRollbackFor)
                .attributes(attributes)
                .build();
    }

    // getters
    public String getGroupId() {
        return groupId;
    }

    public TransactionMode getMode() {
        return mode;
    }

    public TransactionPropagation getPropagation() {
        return propagation;
    }

    public String getTransactionName() {
        return transactionName;
    }

    public int getTimeout() {
        return timeout;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public String getInitiator() {
        return initiator;
    }

    public Class<? extends Throwable>[] getRollbackFor() {
        return rollbackFor.clone();
    }

    public Class<? extends Throwable>[] getNoRollbackFor() {
        return noRollbackFor.clone();
    }

    public Map<String, Object> getAttributes() {
        return new HashMap<>(attributes);
    }

    @Override
    public String toString() {
        return String.format("TransactionPropagationContext{mode=%s, propagation=%s, name='%s'}",
                mode, propagation, transactionName);
    }

    public static class Builder {
        private String groupId;
        private TransactionMode mode = TransactionMode.AT;
        private TransactionPropagation propagation = TransactionPropagation.REQUIRED;
        private String transactionName;
        private int timeout = 30;
        private boolean readOnly = false;
        private String initiator;
        private Class<? extends Throwable>[] rollbackFor = new Class[0];
        private Class<? extends Throwable>[] noRollbackFor = new Class[0];
        private Map<String, Object> attributes = new HashMap<>();

        public Builder groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public Builder mode(TransactionMode mode) {
            this.mode = mode;
            return this;
        }

        public Builder propagation(TransactionPropagation propagation) {
            this.propagation = propagation;
            return this;
        }

        public Builder transactionName(String transactionName) {
            this.transactionName = transactionName;
            return this;
        }

        public Builder timeout(int timeout) {
            this.timeout = timeout;
            return this;
        }

        public Builder readOnly(boolean readOnly) {
            this.readOnly = readOnly;
            return this;
        }

        public Builder initiator(String initiator) {
            this.initiator = initiator;
            return this;
        }

        @SuppressWarnings("unchecked")
        public Builder rollbackFor(Class<? extends Throwable>... rollbackFor) {
            this.rollbackFor = rollbackFor;
            return this;
        }

        @SuppressWarnings("unchecked")
        public Builder noRollbackFor(Class<? extends Throwable>... noRollbackFor) {
            this.noRollbackFor = noRollbackFor;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            this.attributes = new HashMap<>(attributes);
            return this;
        }

        public TransactionPropagationContext build() {
            return new TransactionPropagationContext(this);
        }
    }
}
