package com.tipray.transaction.core.util;

import com.tipray.transaction.core.config.TransactionConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 事务配置工具类
 * 消除重复的配置获取逻辑，提供统一的配置访问方法
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
public class TransactionConfigUtils {

    private static final Logger logger = LoggerFactory.getLogger(TransactionConfigUtils.class);

    /**
     * 配置管理器实例
     */
    private static TransactionConfigurationManager configManager;

    /**
     * 获取配置管理器
     */
    private static TransactionConfigurationManager getConfigManager() {
        if (configManager == null) {
            throw new IllegalStateException("配置管理器未初始化，请先调用setConfigManager()");
        }
        return configManager;
    }

    /**
     * 设置配置管理器
     */
    public static void setConfigManager(TransactionConfigurationManager manager) {
        configManager = manager;
    }

    // ==================== 通用配置获取方法 ====================

    /**
     * 获取字符串配置
     *
     * @param key          配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getString(String key, String defaultValue) {
        try {
            return getConfigManager().getString(key, defaultValue);
        } catch (Exception e) {
            logger.warn("获取配置失败，使用默认值: key={}, default={}, error={}", key, defaultValue, e.getMessage());
            return defaultValue;
        }
    }

    /**
     * 获取整数配置
     *
     * @param key          配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static int getInt(String key, int defaultValue) {
        try {
            return getConfigManager().getInt(key, defaultValue);
        } catch (Exception e) {
            logger.warn("获取配置失败，使用默认值: key={}, default={}, error={}", key, defaultValue, e.getMessage());
            return defaultValue;
        }
    }

    /**
     * 获取长整数配置
     *
     * @param key          配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static long getLong(String key, long defaultValue) {
        try {
            return getConfigManager().getLong(key, defaultValue);
        } catch (Exception e) {
            logger.warn("获取配置失败，使用默认值: key={}, default={}, error={}", key, defaultValue, e.getMessage());
            return defaultValue;
        }
    }

    /**
     * 获取布尔配置
     *
     * @param key          配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static boolean getBoolean(String key, boolean defaultValue) {
        try {
            return getConfigManager().getBoolean(key, defaultValue);
        } catch (Exception e) {
            logger.warn("获取配置失败，使用默认值: key={}, default={}, error={}", key, defaultValue, e.getMessage());
            return defaultValue;
        }
    }

    // ==================== 事务相关配置 ====================

    /**
     * 获取事务超时时间（毫秒）
     */
    public static long getTransactionTimeout() {
        return getLong("tipray.transaction.timeout", 30000L);
    }

    /**
     * 获取事务重试次数
     */
    public static int getTransactionRetryCount() {
        return getInt("tipray.transaction.retry.count", 3);
    }

    /**
     * 获取事务重试间隔（毫秒）
     */
    public static long getTransactionRetryInterval() {
        return getLong("tipray.transaction.retry.interval", 1000L);
    }

    /**
     * 获取是否启用事务监控
     */
    public static boolean isMonitorEnabled() {
        return getBoolean("tipray.transaction.monitor.enabled", true);
    }

    /**
     * 获取监控检查间隔（毫秒）
     */
    public static long getMonitorCheckInterval() {
        return getLong("tipray.transaction.monitor.check-interval", 30000L);
    }

    // ==================== AT模式配置 ====================

    /**
     * 获取AT模式是否启用
     */
    public static boolean isAtModeEnabled() {
        return getBoolean("tipray.transaction.at.enabled", true);
    }

    /**
     * 获取AT模式连接超时时间（毫秒）
     */
    public static long getAtConnectTimeout() {
        return getLong("tipray.transaction.at.connect-timeout", 5000L);
    }

    /**
     * 获取AT模式读取超时时间（毫秒）
     */
    public static long getAtReadTimeout() {
        return getLong("tipray.transaction.at.read-timeout", 30000L);
    }

    /**
     * 获取AT模式默认超时时间（毫秒）
     */
    public static long getAtDefaultTimeout() {
        return getLong("tipray.transaction.at.default-timeout", 30000L);
    }

    // ==================== Saga模式配置 ====================

    /**
     * 获取Saga模式是否启用
     */
    public static boolean isSagaModeEnabled() {
        return getBoolean("tipray.transaction.saga.enabled", true);
    }

    /**
     * 获取Saga模式连接超时时间（毫秒）
     */
    public static long getSagaConnectTimeout() {
        return getLong("tipray.transaction.saga.connect-timeout", 5000L);
    }

    /**
     * 获取Saga模式读取超时时间（毫秒）
     */
    public static long getSagaReadTimeout() {
        return getLong("tipray.transaction.saga.read-timeout", 30000L);
    }

    /**
     * 获取Saga模式默认超时时间（毫秒）
     */
    public static long getSagaDefaultTimeout() {
        return getLong("tipray.transaction.saga.default-timeout", 30000L);
    }

    // ==================== 线程池配置 ====================

    /**
     * 获取异步线程池核心线程数
     */
    public static int getAsyncCorePoolSize() {
        return getInt("tipray.transaction.async.core-pool-size", 5);
    }

    /**
     * 获取异步线程池最大线程数
     */
    public static int getAsyncMaxPoolSize() {
        return getInt("tipray.transaction.async.max-pool-size", 20);
    }

    /**
     * 获取异步线程池队列大小
     */
    public static int getAsyncQueueCapacity() {
        return getInt("tipray.transaction.async.queue-capacity", 1000);
    }

    /**
     * 获取异步线程池空闲时间（秒）
     */
    public static long getAsyncKeepAliveTime() {
        return getLong("tipray.transaction.async.keep-alive-time", 60L);
    }

    // ==================== 异常处理配置 ====================

    /**
     * 获取是否启用全局异常处理器
     */
    public static boolean isGlobalExceptionHandlerEnabled() {
        return getBoolean("tipray.transaction.exception.global-handler-enabled", true);
    }

    /**
     * 获取是否启用异常持久化
     */
    public static boolean isExceptionPersistenceEnabled() {
        return getBoolean("tipray.transaction.exception.persistence-enabled", true);
    }

    /**
     * 获取错误信息最大长度
     */
    public static int getErrorMessageMaxLength() {
        return getInt("tipray.transaction.exception.message-max-length", 200);
    }

    /**
     * 获取是否返回详细错误信息
     */
    public static boolean isDetailedErrorEnabled() {
        return getBoolean("tipray.transaction.exception.detailed-error-enabled", false);
    }

    // ==================== 持久化配置 ====================

    /**
     * 获取批量处理大小
     */
    public static int getBatchSize() {
        return getInt("tipray.transaction.persistence.batch-size", 100);
    }

    /**
     * 获取批量处理间隔（毫秒）
     */
    public static long getBatchInterval() {
        return getLong("tipray.transaction.persistence.batch-interval", 1000L);
    }

    /**
     * 获取是否启用异步持久化
     */
    public static boolean isAsyncPersistenceEnabled() {
        return getBoolean("tipray.transaction.persistence.async-enabled", true);
    }

    // ==================== UI配置 ====================

    /**
     * 获取是否启用UI
     */
    public static boolean isUiEnabled() {
        return getBoolean("tipray.transaction.ui.enabled", true);
    }

    /**
     * 获取UI访问路径
     */
    public static String getUiPath() {
        return getString("tipray.transaction.ui.path", "/tipray-transaction-ui");
    }

    /**
     * 获取UI页面标题
     */
    public static String getUiTitle() {
        return getString("tipray.transaction.ui.title", "Tipray分布式事务监控");
    }

    /**
     * 获取UI刷新间隔（秒）
     */
    public static int getUiRefreshInterval() {
        return getInt("tipray.transaction.ui.refresh-interval", 3);
    }

    /**
     * 获取UI分页大小
     */
    public static int getUiPageSize() {
        return getInt("tipray.transaction.ui.page-size", 15);
    }

    /**
     * 获取数据保留天数
     */
    public static int getDataRetentionDays() {
        return getInt("tipray.transaction.ui.data-retention-days", 7);
    }

    // ==================== 开发环境配置 ====================

    /**
     * 获取是否启用开发模式
     */
    public static boolean isDevModeEnabled() {
        return getBoolean("tipray.transaction.dev.enabled", false);
    }

    /**
     * 获取是否启用调试日志
     */
    public static boolean isDebugLogEnabled() {
        return getBoolean("tipray.transaction.dev.debug-log-enabled", false);
    }

    /**
     * 获取是否启用性能监控
     */
    public static boolean isPerformanceMonitorEnabled() {
        return getBoolean("tipray.transaction.dev.performance-monitor-enabled", false);
    }

    // ==================== 配置验证方法 ====================

    /**
     * 验证配置的合理性
     *
     * @return 验证结果
     */
    public static boolean validateConfiguration() {
        try {
            // 验证超时配置
            if (getTransactionTimeout() <= 0) {
                logger.error("事务超时时间配置无效: {}", getTransactionTimeout());
                return false;
            }

            // 验证重试配置
            if (getTransactionRetryCount() < 0) {
                logger.error("事务重试次数配置无效: {}", getTransactionRetryCount());
                return false;
            }

            // 验证线程池配置
            if (getAsyncCorePoolSize() <= 0 || getAsyncMaxPoolSize() <= 0) {
                logger.error("线程池配置无效: core={}, max={}", getAsyncCorePoolSize(), getAsyncMaxPoolSize());
                return false;
            }

            if (getAsyncCorePoolSize() > getAsyncMaxPoolSize()) {
                logger.error("线程池核心线程数不能大于最大线程数: core={}, max={}",
                        getAsyncCorePoolSize(), getAsyncMaxPoolSize());
                return false;
            }

            logger.info("配置验证通过");
            return true;

        } catch (Exception e) {
            logger.error("配置验证失败", e);
            return false;
        }
    }
}
