package com.tipray.transaction.core.domain.transaction;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.tipray.transaction.core.enums.BranchTransactionStatus;
import com.tipray.transaction.core.enums.BranchType;
import com.tipray.transaction.core.util.IdGenerator;


import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 事务分支实体
 * 表示分布式事务中的一个执行步骤
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
public class BranchTransactionDO {

    /**
     * 步骤ID
     */
    private Long branchTransactionId;

    /**
     * 所属事务ID
     */
    private String transactionId;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 步骤顺序
     */
    private Integer stepOrder;

    /**
     * 步骤状态
     */
    private BranchTransactionStatus status;

    /**
     * 分支类型
     */
    private BranchType branchType;

    /**
     * 目标服务名称
     */
    private String targetService;

    /**
     * 目标方法名称
     */
    private String targetMethod;

    /**
     * 补偿方法名称（Saga模式使用）
     */
    private String compensationMethod;

    /**
     * 是否为关键步骤
     * 关键步骤失败会导致整个事务失败
     */
    private Boolean critical;

    /**
     * 步骤参数（JSON格式）
     */
    private String stepParams;

    /**
     * 步骤结果（JSON格式）
     */
    private String result;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 当前重试次数
     */
    private Integer currentRetryCount;

    /**
     * 超时时间（毫秒）
     */
    private Long timeoutMillis;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 扩展属性（JSON格式）
     */
    private String extendProperties;

    /**
     * 是否启用事务屏障
     */
    private boolean enableBarrier;



    /**
     * 私有构造函数
     */
    private BranchTransactionDO() {
        this.currentRetryCount = 0;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.branchType = BranchType.REMOTE; // 默认为远程分支
    }

    /**
     * 创建新的事务步骤
     *
     * @param stepName      步骤名称
     * @param targetService 目标服务
     * @param targetMethod  目标方法
     * @return 事务步骤实例
     */
    public static BranchTransactionDO create(String stepName, String targetService, String targetMethod) {
        if (StrUtil.isBlank(stepName)) {
            throw new IllegalArgumentException("步骤名称不能为空");
        }
        if (StrUtil.isBlank(targetService)) {
            throw new IllegalArgumentException("目标服务不能为空");
        }
        if (StrUtil.isBlank(targetMethod)) {
            throw new IllegalArgumentException("目标方法不能为空");
        }

        BranchTransactionDO step = new BranchTransactionDO();
        step.branchTransactionId = IdGenerator.generateBranchId();
        step.stepName = stepName;
        step.targetService = targetService;
        step.targetMethod = targetMethod;
        step.status = BranchTransactionStatus.REGISTERED;
        step.critical = true; // 默认为关键步骤
        step.retryCount = 0; // 默认不重试，应该由调用方设置
        step.timeoutMillis = 30000L; // 默认30秒超时，应该由调用方设置

        return step;
    }

    /**
     * 生成步骤ID
     *
     * @return 步骤ID
     */
    private static String generateStepId() {
        return "STEP_" + System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID().substring(0, 6).toUpperCase();
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.currentRetryCount++;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 判断是否超过最大重试次数
     *
     * @return true表示超过最大重试次数
     */
    public boolean isRetryExceeded() {
        // 只有当重试次数大于0且当前重试次数达到或超过最大重试次数时，才算重试超限
        if (this.retryCount == null || this.retryCount <= 0) {
            return false; // 没有配置重试，不算超限
        }
        if (this.currentRetryCount == null) {
            return false; // 没有重试过，不算超限
        }
        return this.currentRetryCount >= this.retryCount;
    }

    /**
     * 判断是否为本地分支
     *
     * @return true表示本地分支
     */
    public boolean isLocalBranch() {
        return this.branchType == BranchType.LOCAL;
    }

    /**
     * 判断是否为远程分支
     *
     * @return true表示远程分支
     */
    public boolean isRemoteBranch() {
        return this.branchType == BranchType.REMOTE;
    }

    /**
     * 创建本地分支
     *
     * @param stepName      步骤名称
     * @param targetMethod  目标方法
     * @return 本地分支实例
     */
    public static BranchTransactionDO createLocalBranch(String stepName, String targetMethod) {
        BranchTransactionDO localBranch = new BranchTransactionDO();
        localBranch.branchTransactionId = IdGenerator.generateBranchId();
        localBranch.stepName = stepName;
        localBranch.targetService = "LOCAL";
        localBranch.targetMethod = targetMethod;
        localBranch.status = BranchTransactionStatus.REGISTERED;
        localBranch.branchType = BranchType.LOCAL;
        localBranch.critical = true; // 本地分支是关键分支
        localBranch.retryCount = 0; // 本地分支不重试
        localBranch.timeoutMillis = 30000L; // 默认30秒超时

        return localBranch;
    }

    // Getter和Setter方法
    public Long getBranchTransactionId() {
        return branchTransactionId;
    }

    public void setBranchTransactionId(Long branchTransactionId) {
        this.branchTransactionId = branchTransactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        this.updateTime = LocalDateTime.now();
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
        this.updateTime = LocalDateTime.now();
    }

    public Integer getStepOrder() {
        return stepOrder;
    }

    public void setStepOrder(Integer stepOrder) {
        this.stepOrder = stepOrder;
        this.updateTime = LocalDateTime.now();
    }

    public BranchTransactionStatus getStatus() {
        return status;
    }

    public void setStatus(BranchTransactionStatus status) {
        this.status = status;
        this.updateTime = LocalDateTime.now();
    }

    public String getTargetService() {
        return targetService;
    }

    public void setTargetService(String targetService) {
        this.targetService = targetService;
        this.updateTime = LocalDateTime.now();
    }

    public String getTargetMethod() {
        return targetMethod;
    }

    public void setTargetMethod(String targetMethod) {
        this.targetMethod = targetMethod;
        this.updateTime = LocalDateTime.now();
    }

    public String getCompensationMethod() {
        return compensationMethod;
    }

    public void setCompensationMethod(String compensationMethod) {
        this.compensationMethod = compensationMethod;
        this.updateTime = LocalDateTime.now();
    }

    public Boolean getCritical() {
        return critical;
    }

    public void setCritical(Boolean critical) {
        this.critical = critical;
        this.updateTime = LocalDateTime.now();
    }

    public String getStepParams() {
        return stepParams;
    }

    public void setStepParams(String stepParams) {
        this.stepParams = stepParams;
        this.updateTime = LocalDateTime.now();
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
        this.updateTime = LocalDateTime.now();
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
        this.updateTime = LocalDateTime.now();
    }

    public Integer getCurrentRetryCount() {
        return currentRetryCount;
    }

    public void setCurrentRetryCount(Integer currentRetryCount) {
        this.currentRetryCount = currentRetryCount;
        this.updateTime = LocalDateTime.now();
    }

    public Long getTimeoutMillis() {
        return timeoutMillis;
    }

    public void setTimeoutMillis(Long timeoutMillis) {
        this.timeoutMillis = timeoutMillis;
        this.updateTime = LocalDateTime.now();
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        this.updateTime = LocalDateTime.now();
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        this.updateTime = LocalDateTime.now();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.updateTime = LocalDateTime.now();
    }

    public String getExtendProperties() {
        return extendProperties;
    }

    public void setExtendProperties(String extendProperties) {
        this.extendProperties = extendProperties;
        this.updateTime = LocalDateTime.now();
    }

    public boolean isEnableBarrier() {
        return enableBarrier;
    }

    public void setEnableBarrier(boolean enableBarrier) {
        this.enableBarrier = enableBarrier;
        this.updateTime = LocalDateTime.now();
    }

    public BranchType getBranchType() {
        return branchType;
    }

    public void setBranchType(BranchType branchType) {
        this.branchType = branchType;
        this.updateTime = LocalDateTime.now();
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BranchTransactionDO that = (BranchTransactionDO) o;
        return Objects.equals(branchTransactionId, that.branchTransactionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(branchTransactionId);
    }

    @Override
    public String toString() {
        return String.format("DistributedBranchTransaction{stepId='%s', transactionId='%s', stepName='%s', status=%s, order=%d}",
                branchTransactionId, transactionId, stepName, status, stepOrder);
    }
}
