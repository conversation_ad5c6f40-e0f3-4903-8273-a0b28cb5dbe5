package com.tipray.transaction.core.config.propertie;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import javax.validation.constraints.Min;

/**
 * Tipray分布式事务配置属性
 * 统一的配置属性类，所有配置都从这里获取
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
@ConfigurationProperties(prefix = "tipray.transaction")
public class TiprayTransactionProperties {

    /**
     * 是否启用分布式事务
     */
    private boolean enabled = true;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * AT模式配置
     */
    private AtConfig at = new AtConfig();

    /**
     * Saga模式配置
     */
    private SagaConfig saga = new SagaConfig();

    /**
     * 重试配置
     */
    private RetryConfig retry = new RetryConfig();

    /**
     * 线程池配置
     */
    private ThreadPoolConfig threadPool = new ThreadPoolConfig();

    /**
     * 超时配置
     */
    private TimeoutConfig timeout = new TimeoutConfig();

    /**
     * HTTP配置
     */
    private HttpConfig http = new HttpConfig();

    /**
     * 持久化配置
     */
    private PersistenceConfig persistence = new PersistenceConfig();

    /**
     * 异常处理配置
     */
    private ExceptionConfig exception = new ExceptionConfig();

    /**
     * 监控配置
     */
    private MonitorConfig monitor = new MonitorConfig();

    /**
     * UI配置
     */
    private UiConfig ui = new UiConfig();

    /**
     * 开发环境配置
     */
    private DevConfig dev = new DevConfig();

    /**
     * AT模式配置
     */
    @Data
    public static class AtConfig {
        /**
         * 是否启用AT模式
         */
        private boolean enabled = true;

        /**
         * 默认连接超时时间（毫秒）
         */
        @Min(1000)
        private long defaultConnectTimeout = 5000L;

        /**
         * 默认读取超时时间（毫秒）
         */
        @Min(1000)
        private long defaultReadTimeout = 30000L;

        /**
         * 默认执行超时时间（毫秒）
         */
        @Min(1000)
        private long defaultTimeout = 30000L;

        /**
         * 服务特定配置
         */
        private ServiceConfig service = new ServiceConfig();

        /**
         * 数据源代理配置
         */
        private DataSourceProxyConfig dataSourceProxy = new DataSourceProxyConfig();

        /**
         * UndoLog配置
         */
        private UndoLogConfig undoLog = new UndoLogConfig();
    }

    /**
     * 服务特定配置
     */
    @Data
    public static class ServiceConfig {
        /**
         * 服务映射配置
         * key: 服务名称, value: 服务映射配置
         */
        private java.util.Map<String, ServiceMappingConfig> mappings = new java.util.HashMap<>();
    }

    /**
     * 服务映射配置
     */
    @Data
    public static class ServiceMappingConfig {
        /**
         * 服务基础URL
         */
        private String baseUrl = "";

        /**
         * 协议
         */
        private String protocol = "";

        /**
         * 端口
         */
        private int port = 0;

        /**
         * 提交路径
         */
        private String commitPath = "/api/transaction/commit";

        /**
         * 回滚路径
         */
        private String rollbackPath = "/api/transaction/rollback";

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 5000;

        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 30000;

        /**
         * 重试次数
         */
        private int retryCount = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long retryInterval = 1000L;
    }

    /**
     * Saga模式配置
     */
    @Data
    public static class SagaConfig {
        /**
         * 是否启用Saga模式
         */
        private boolean enabled = true;

        /**
         * 默认补偿超时时间（秒）
         */
        @Min(1)
        private long defaultCompensationTimeout = 30L;

        /**
         * 补偿配置
         */
        private CompensationConfig compensation = new CompensationConfig();
    }

    /**
     * 补偿配置
     */
    @Data
    public static class CompensationConfig {
        /**
         * 默认最大重试次数
         */
        @Min(0)
        private int defaultMaxRetries = 3;

        /**
         * 默认重试间隔（毫秒）
         */
        @Min(100)
        private long defaultRetryInterval = 1000L;

        /**
         * 是否启用异步补偿
         */
        private boolean asyncEnabled = true;
    }

    /**
     * 重试配置
     */
    @Data
    public static class RetryConfig {
        /**
         * 默认最大重试次数
         */
        @Min(0)
        private int defaultMaxRetries = 3;

        /**
         * 默认重试间隔（毫秒）
         */
        @Min(100)
        private long defaultRetryInterval = 1000L;

        /**
         * 默认退避倍数
         */
        @Min(1)
        private double defaultBackoffMultiplier = 2.0;

        /**
         * 最大重试间隔（毫秒）
         */
        @Min(1000)
        private long maxRetryInterval = 60000L;
    }

    /**
     * 线程池配置
     */
    @Data
    public static class ThreadPoolConfig {
        /**
         * 默认核心线程数
         */
        @Min(1)
        private int defaultCoreSize = 5;

        /**
         * 默认最大线程数
         */
        @Min(1)
        private int defaultMaxSize = 20;

        /**
         * 默认队列容量
         */
        @Min(10)
        private int defaultQueueCapacity = 1000;

        /**
         * 线程存活时间（毫秒）
         */
        @Min(1000)
        private long keepAliveTime = 60000L;

        /**
         * 重试线程池配置
         */
        private PoolConfig retry = new PoolConfig(8, 16, 500);

        /**
         * 补偿线程池配置
         */
        private PoolConfig compensation = new PoolConfig(3, 10, 200);

        /**
         * 定时任务线程池配置
         */
        private PoolConfig scheduled = new PoolConfig(2, 5, 100);
    }

    /**
     * 线程池具体配置
     */
    @Data
    public static class PoolConfig {
        private int coreSize;
        private int maxSize;
        private int queueCapacity;

        public PoolConfig() {
        }

        public PoolConfig(int coreSize, int maxSize, int queueCapacity) {
            this.coreSize = coreSize;
            this.maxSize = maxSize;
            this.queueCapacity = queueCapacity;
        }
    }

    /**
     * 超时配置
     */
    @Data
    public static class TimeoutConfig {
        /**
         * 默认执行超时时间（毫秒）
         */
        @Min(1000)
        private long defaultExecutionTimeout = 30000L;

        /**
         * 默认连接超时时间（毫秒）
         */
        @Min(1000)
        private long defaultConnectTimeout = 5000L;

        /**
         * 默认读取超时时间（毫秒）
         */
        @Min(1000)
        private long defaultReadTimeout = 30000L;
    }

    /**
     * HTTP配置
     */
    @Data
    public static class HttpConfig {
        /**
         * 默认连接超时时间（毫秒）
         */
        @Min(1000)
        private long defaultConnectTimeout = 5000L;

        /**
         * 默认读取超时时间（毫秒）
         */
        @Min(1000)
        private long defaultReadTimeout = 30000L;

        /**
         * 最大连接数
         */
        @Min(1)
        private int maxConnections = 200;

        /**
         * 每个路由的最大连接数
         */
        @Min(1)
        private int maxConnectionsPerRoute = 50;
    }

    /**
     * 数据源代理配置
     */
    @Data
    public static class DataSourceProxyConfig {
        /**
         * 是否启用数据源代理
         */
        private boolean enabled = true;

        /**
         * 排除的数据源Bean名称
         */
        private String[] excludes = {};

        /**
         * 连接超时时间（毫秒）
         */
        @Min(1000)
        private int connectionTimeout = 30000;

        /**
         * 查询超时时间（毫秒）
         */
        @Min(1000)
        private int queryTimeout = 60000;
    }

    /**
     * UndoLog配置
     */
    @Data
    public static class UndoLogConfig {
        /**
         * 是否启用自动清理
         */
        private boolean cleanupEnabled = true;

        /**
         * 清理间隔（小时）
         */
        @Min(1)
        private int cleanupIntervalHours = 6;

        /**
         * 数据保留天数
         */
        @Min(1)
        private int retentionDays = 7;

        /**
         * 批量删除大小
         */
        @Min(100)
        private int batchSize = 1000;
    }

    /**
     * 持久化配置
     */
    @Data
    public static class PersistenceConfig {
        /**
         * 异步线程池核心线程数
         */
        private int corePoolSize = 2;

        /**
         * 异步线程池最大线程数
         */
        private int maximumPoolSize = 4;

        /**
         * 线程空闲时间（秒）
         */
        private long keepAliveTime = 60L;

        /**
         * 任务队列容量
         */
        private int queueCapacity = 1000;

        /**
         * 批量处理大小
         */
        private int batchSize = 50;

        /**
         * 批量处理间隔（毫秒）
         */
        private long batchInterval = 1000L;
    }

    /**
     * 异常处理配置
     */
    @Data
    public static class ExceptionConfig {
        /**
         * 是否启用全局异常处理器
         */
        private boolean globalHandlerEnabled = true;

        /**
         * 是否启用异常持久化
         */
        private boolean persistenceEnabled = true;

        /**
         * 是否返回详细错误信息
         */
        private boolean detailedErrorEnabled = false;

        /**
         * 错误信息最大长度
         */
        @Min(50)
        private int messageMaxLength = 200;
    }

    /**
     * 监控配置
     */
    @Data
    public static class MonitorConfig {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 检查间隔（毫秒）
         */
        @Min(1000)
        private long checkInterval = 30000L;
    }

    /**
     * UI配置
     */
    @Data
    public static class UiConfig {
        /**
         * 是否启用UI
         */
        private boolean enabled = true;

        /**
         * UI访问路径
         */
        private String path = "/tipray-transaction-ui";

        /**
         * 页面标题
         */
        private String title = "Tipray分布式事务监控";

        /**
         * 刷新间隔（秒）
         */
        @Min(1)
        private int refreshInterval = 3;

        /**
         * 分页大小
         */
        @Min(5)
        private int pageSize = 15;

        /**
         * 数据保留天数
         */
        @Min(1)
        private int dataRetentionDays = 7;
    }

    /**
     * 开发环境配置
     */
    @Data
    public static class DevConfig {
        /**
         * 是否启用开发模式
         */
        private boolean enabled = false;

        /**
         * 是否启用调试日志
         */
        private boolean debugLogEnabled = false;

        /**
         * 是否启用性能监控
         */
        private boolean performanceMonitorEnabled = false;
    }
}
