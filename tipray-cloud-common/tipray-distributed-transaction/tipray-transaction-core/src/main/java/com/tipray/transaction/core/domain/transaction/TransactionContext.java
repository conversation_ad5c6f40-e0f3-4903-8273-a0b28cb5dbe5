package com.tipray.transaction.core.domain.transaction;

import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionPropagation;
import com.tipray.transaction.core.enums.TransactionStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 事务上下文
 * 包含事务执行过程中的所有信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionContext {

    private final String transactionId;
    private final String groupId;
    private final TransactionMode mode;
    private final TransactionPropagation propagation;
    private final boolean readOnly;
    private final long timeout;
    private final LocalDateTime startTime;
    private final String applicationName;
    private final String methodSignature;
    private final Map<String, Object> attributes;
    private final List<String> serviceEndpoints;
    private final boolean asyncCommitOrRollback;
    private final boolean enableBarrier;
    // 嵌套事务相关
    private final TransactionContext parent;
    private final List<TransactionContext> children;
    private final int nestingLevel;
    // 当前分支事务相关
    private Long branchId;
    // 挂起恢复相关
    private volatile boolean suspended;
    private volatile LocalDateTime suspendTime;
    private volatile String suspendReason;

    // 运行时状态
    private volatile TransactionStatus currentStatus;
    private volatile LocalDateTime lastUpdateTime;

    private TransactionContext(Builder builder) {
        this.transactionId = builder.transactionId;
        this.groupId = builder.groupId;
        this.mode = builder.mode;
        this.propagation = builder.propagation;
        this.readOnly = builder.readOnly;
        this.timeout = builder.timeout;
        this.startTime = LocalDateTime.now();
        this.applicationName = builder.applicationName;
        this.methodSignature = builder.methodSignature;
        this.attributes = new ConcurrentHashMap<>(builder.attributes);
        this.serviceEndpoints = new ArrayList<>(builder.serviceEndpoints);
        this.parent = builder.parent;
        this.children = new CopyOnWriteArrayList<>();
        this.nestingLevel = parent != null ? parent.nestingLevel + 1 : 0;
        this.suspended = false;
        this.currentStatus = TransactionStatus.UNKNOWN;
        this.lastUpdateTime = this.startTime;
        this.asyncCommitOrRollback = builder.asyncCommitOrRollback;
        this.enableBarrier = builder.enableBarrier;
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建子事务上下文
     */
    public TransactionContext createChild(String childTransactionId, TransactionMode childMode) {
        TransactionContext child = TransactionContext.builder()
                .transactionId(childTransactionId)
                .groupId(this.groupId)
                .mode(childMode)
                .propagation(TransactionPropagation.NESTED)
                .parent(this)
                .timeout(this.timeout)
                .applicationName(this.applicationName)
                .methodSignature(this.methodSignature)
                .build();

        this.children.add(child);
        return child;
    }

    /**
     * 挂起事务
     */
    public void suspend(String reason) {
        this.suspended = true;
        this.suspendTime = LocalDateTime.now();
        this.suspendReason = reason;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 恢复事务
     */
    public void resume() {
        this.suspended = false;
        this.suspendTime = null;
        this.suspendReason = null;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 更新状态
     */
    public void updateStatus(TransactionStatus status) {
        this.currentStatus = status;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 判断是否为根事务
     */
    public boolean isRoot() {
        return parent == null;
    }

    /**
     * 获取根事务上下文
     */
    public TransactionContext getRoot() {
        TransactionContext current = this;
        while (current.parent != null) {
            current = current.parent;
        }
        return current;
    }

    /**
     * 获取事务层级路径
     */
    public String getHierarchyPath() {
        if (isRoot()) {
            return transactionId;
        }

        List<String> path = new ArrayList<>();
        TransactionContext current = this;
        while (current != null) {
            path.add(0, current.transactionId);
            current = current.parent;
        }

        return String.join(" -> ", path);
    }

    /**
     * 添加属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    /**
     * 移除属性
     */
    public Object removeAttribute(String key) {
        Object removed = attributes.remove(key);
        if (removed != null) {
            this.lastUpdateTime = LocalDateTime.now();
        }
        return removed;
    }

    /**
     * 添加服务端点
     */
    public void addServiceEndpoint(String endpoint) {
        if (!serviceEndpoints.contains(endpoint)) {
            serviceEndpoints.add(endpoint);
            this.lastUpdateTime = LocalDateTime.now();
        }
    }

    /**
     * 获取事务执行时长（毫秒）
     */
    public long getDurationMillis() {
        return java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
    }

    /**
     * 判断是否超时
     */
    public boolean isTimeout() {
        return getDurationMillis() > timeout;
    }

    /**
     * 获取剩余超时时间（毫秒）
     */
    public long getRemainingTimeoutMillis() {
        long elapsed = getDurationMillis();
        return Math.max(0, timeout - elapsed);
    }

    /**
     * 判断事务是否活跃
     */
    public boolean isActive() {
        return !currentStatus.isTerminal() && !suspended;
    }

    /**
     * 获取所有子事务ID
     */
    public List<String> getChildTransactionIds() {
        return children.stream()
                .map(TransactionContext::getTransactionId)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 转换为持久化对象
     */
    public TransactionDO toTransactionDO() {
        return TransactionDO.builder()
                .transactionId(transactionId)
                .groupId(groupId)
                .mode(mode.name())
                .propagation(propagation.name())
                .status(currentStatus.name())
                .applicationName(applicationName)
                .methodSignature(methodSignature)
                .timeout(timeout)
                .readOnly(readOnly)
                .startTime(startTime)
                .lastUpdateTime(lastUpdateTime)
                .parentTransactionId(parent != null ? parent.transactionId : null)
                .nestingLevel(nestingLevel)
                .suspended(suspended)
                .suspendReason(suspendReason)
                .attributes(serializeAttributes())
                .serviceEndpoints(String.join(",", serviceEndpoints))
                .build();
    }

    /**
     * 转换为简要信息
     */
    public TransactionSummary toSummary() {
        return new TransactionSummary(
                transactionId,
                groupId,
                mode,
                currentStatus,
                applicationName,
                methodSignature,
                startTime != null ? startTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() : 0L,
                System.currentTimeMillis(),
                getDurationMillis(),
                readOnly
        );
    }

    /**
     * 序列化属性
     */
    private String serializeAttributes() {
        if (attributes.isEmpty()) {
            return "{}";
        }

        try {
            // 这里应该使用JSON序列化工具，简化示例
            StringBuilder sb = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                if (!first) sb.append(",");
                sb.append("\"").append(entry.getKey()).append("\":\"")
                        .append(entry.getValue()).append("\"");
                first = false;
            }
            sb.append("}");
            return sb.toString();
        } catch (Exception e) {
            return "{}";
        }
    }

    // getters
    public String getTransactionId() {
        return transactionId;
    }

    public String getGroupId() {
        return groupId;
    }

    public TransactionMode getMode() {
        return mode;
    }

    public TransactionPropagation getPropagation() {
        return propagation;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public long getTimeout() {
        return timeout;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public String getMethodSignature() {
        return methodSignature;
    }

    public Map<String, Object> getAttributes() {
        return new HashMap<>(attributes);
    }

    public List<String> getServiceEndpoints() {
        return new ArrayList<>(serviceEndpoints);
    }

    public TransactionContext getParent() {
        return parent;
    }

    public List<TransactionContext> getChildren() {
        return new ArrayList<>(children);
    }

    public int getNestingLevel() {
        return nestingLevel;
    }

    public boolean isSuspended() {
        return suspended;
    }

    public LocalDateTime getSuspendTime() {
        return suspendTime;
    }

    public String getSuspendReason() {
        return suspendReason;
    }

    public TransactionStatus getCurrentStatus() {
        return currentStatus;
    }

    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    public boolean isAsyncCommitOrRollback() {
        return asyncCommitOrRollback;
    }

    public boolean isEnableBarrier() {
        return enableBarrier;
    }

    public Long getBranchId() {
        return branchId;
    }

    public void setBranchId(Long branchId) {
        this.branchId = branchId;
    }

    @Override
    public String toString() {
        return String.format("TransactionContext{id='%s', mode=%s, status=%s, level=%d, active=%s}",
                transactionId, mode, currentStatus, nestingLevel, isActive());
    }

    public static class Builder {
        private String transactionId;
        private String groupId;
        private TransactionMode mode = TransactionMode.AT;
        private TransactionPropagation propagation = TransactionPropagation.REQUIRED;
        private boolean readOnly = false;
        private long timeout = 30000;
        private String applicationName;
        private String methodSignature;
        private Map<String, Object> attributes = new HashMap<>();
        private List<String> serviceEndpoints = new ArrayList<>();
        private TransactionDefinition definition;
        private TransactionContext parent;
        private boolean asyncCommitOrRollback = false;
        private boolean enableBarrier = false;

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public Builder mode(TransactionMode mode) {
            this.mode = mode;
            return this;
        }

        public Builder propagation(TransactionPropagation propagation) {
            this.propagation = propagation;
            return this;
        }

        public Builder readOnly(boolean readOnly) {
            this.readOnly = readOnly;
            return this;
        }

        public Builder timeout(long timeout) {
            this.timeout = timeout;
            return this;
        }

        public Builder applicationName(String applicationName) {
            this.applicationName = applicationName;
            return this;
        }

        public Builder methodSignature(String methodSignature) {
            this.methodSignature = methodSignature;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder serviceEndpoint(String endpoint) {
            this.serviceEndpoints.add(endpoint);
            return this;
        }

        public Builder parent(TransactionContext parent) {
            this.parent = parent;
            return this;
        }

        public Builder asyncCommitOrRollback(boolean asyncCommitOrRollback) {
            this.asyncCommitOrRollback = asyncCommitOrRollback;
            return this;
        }

        public Builder enableBarrier(boolean enableBarrier) {
            this.enableBarrier = enableBarrier;
            return this;
        }

        public TransactionContext build() {
            if (transactionId == null || transactionId.trim().isEmpty()) {
                throw new IllegalArgumentException("事务ID不能为空");
            }
            if (applicationName == null || applicationName.trim().isEmpty()) {
                throw new IllegalArgumentException("应用名称不能为空");
            }

            return new TransactionContext(this);
        }
    }

    /**
     * 事务摘要信息
     */
    public static class TransactionSummary {
        private final String transactionId;
        private final String groupId;
        private final TransactionMode mode;
        private final TransactionStatus status;
        private final String applicationName;
        private final String methodSignature;
        private final long startTime;
        private final long endTime;
        private final long duration;
        private final boolean readOnly;

        public TransactionSummary(String transactionId, String groupId, TransactionMode mode,
                                  TransactionStatus status, String applicationName, String methodSignature,
                                  long startTime, long endTime, long duration, boolean readOnly) {
            this.transactionId = transactionId;
            this.groupId = groupId;
            this.mode = mode;
            this.status = status;
            this.applicationName = applicationName;
            this.methodSignature = methodSignature;
            this.startTime = startTime;
            this.endTime = endTime;
            this.duration = duration;
            this.readOnly = readOnly;
        }

        // getters
        public String getTransactionId() {
            return transactionId;
        }

        public String getGroupId() {
            return groupId;
        }

        public TransactionMode getMode() {
            return mode;
        }

        public TransactionStatus getStatus() {
            return status;
        }

        public String getApplicationName() {
            return applicationName;
        }

        public String getMethodSignature() {
            return methodSignature;
        }

        public long getStartTime() {
            return startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public long getDuration() {
            return duration;
        }

        public boolean isReadOnly() {
            return readOnly;
        }


        @Override
        public String toString() {
            return String.format("TransactionSummary{id='%s', mode=%s, status=%s, duration=%dms}",
                    transactionId, mode, status, duration);
        }
    }
}
