package com.tipray.transaction.core.domain.transaction;


import com.tipray.transaction.core.enums.TransactionStatus;

import java.time.LocalDateTime;

/**
 * 事务数据对象
 * 用于数据库持久化的事务信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
public class TransactionDO {

    private String transactionId;
    private String groupId;
    private String mode;
    private String propagation;
    private String status;
    private String applicationName;
    private String methodSignature;
    /**
     * 超时时间  秒
     */
    private Long timeout;
    private Boolean readOnly;
    private LocalDateTime startTime;
    private LocalDateTime lastUpdateTime;
    private String parentTransactionId;
    private Integer nestingLevel;
    private Boolean suspended;
    private String suspendReason;
    private String attributes;
    private String serviceEndpoints;

    private TransactionDO(Builder builder) {
        this.transactionId = builder.transactionId;
        this.groupId = builder.groupId;
        this.mode = builder.mode;
        this.propagation = builder.propagation;
        this.status = builder.status;
        this.applicationName = builder.applicationName;
        this.methodSignature = builder.methodSignature;
        this.timeout = builder.timeout;
        this.readOnly = builder.readOnly;
        this.startTime = builder.startTime;
        this.lastUpdateTime = builder.lastUpdateTime;
        this.parentTransactionId = builder.parentTransactionId;
        this.nestingLevel = builder.nestingLevel;
        this.suspended = builder.suspended;
        this.suspendReason = builder.suspendReason;
        this.attributes = builder.attributes;
        this.serviceEndpoints = builder.serviceEndpoints;
    }

    public static Builder builder() {
        return new Builder();
    }

    // getters and setters
    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getPropagation() {
        return propagation;
    }

    public void setPropagation(String propagation) {
        this.propagation = propagation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setStatus(TransactionStatus status) {
        this.status = status != null ? status.name() : null;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getMethodSignature() {
        return methodSignature;
    }

    public void setMethodSignature(String methodSignature) {
        this.methodSignature = methodSignature;
    }

    public Long getTimeout() {
        return timeout;
    }

    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getParentTransactionId() {
        return parentTransactionId;
    }

    public void setParentTransactionId(String parentTransactionId) {
        this.parentTransactionId = parentTransactionId;
    }

    public Integer getNestingLevel() {
        return nestingLevel;
    }

    public void setNestingLevel(Integer nestingLevel) {
        this.nestingLevel = nestingLevel;
    }

    public Boolean getSuspended() {
        return suspended;
    }

    public void setSuspended(Boolean suspended) {
        this.suspended = suspended;
    }

    public String getSuspendReason() {
        return suspendReason;
    }

    public void setSuspendReason(String suspendReason) {
        this.suspendReason = suspendReason;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public String getServiceEndpoints() {
        return serviceEndpoints;
    }

    public void setServiceEndpoints(String serviceEndpoints) {
        this.serviceEndpoints = serviceEndpoints;
    }

    @Override
    public String toString() {
        return String.format("TransactionDO{id='%s', status='%s', app='%s'}",
                transactionId, status, applicationName);
    }

    public static class Builder {
        private String transactionId;
        private String groupId;
        private String mode;
        private String propagation;
        private String status;
        private String applicationName;
        private String methodSignature;
        private Long timeout;
        private Boolean readOnly;
        private LocalDateTime startTime;
        private LocalDateTime lastUpdateTime;
        private String parentTransactionId;
        private Integer nestingLevel;
        private Boolean suspended;
        private String suspendReason;
        private String attributes;
        private String serviceEndpoints;

        public Builder transactionId(String transactionId) {
            this.transactionId = transactionId;
            return this;
        }

        public Builder groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public Builder mode(String mode) {
            this.mode = mode;
            return this;
        }

        public Builder propagation(String propagation) {
            this.propagation = propagation;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder applicationName(String applicationName) {
            this.applicationName = applicationName;
            return this;
        }

        public Builder methodSignature(String methodSignature) {
            this.methodSignature = methodSignature;
            return this;
        }

        public Builder timeout(Long timeout) {
            this.timeout = timeout;
            return this;
        }

        public Builder readOnly(Boolean readOnly) {
            this.readOnly = readOnly;
            return this;
        }

        public Builder startTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder lastUpdateTime(LocalDateTime lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
            return this;
        }

        public Builder parentTransactionId(String parentTransactionId) {
            this.parentTransactionId = parentTransactionId;
            return this;
        }

        public Builder nestingLevel(Integer nestingLevel) {
            this.nestingLevel = nestingLevel;
            return this;
        }

        public Builder suspended(Boolean suspended) {
            this.suspended = suspended;
            return this;
        }

        public Builder suspendReason(String suspendReason) {
            this.suspendReason = suspendReason;
            return this;
        }

        public Builder attributes(String attributes) {
            this.attributes = attributes;
            return this;
        }

        public Builder serviceEndpoints(String serviceEndpoints) {
            this.serviceEndpoints = serviceEndpoints;
            return this;
        }

        public TransactionDO build() {
            return new TransactionDO(this);
        }
    }
}
