package com.tipray.transaction.core.config;

import com.tipray.transaction.core.config.propertie.TiprayTransactionProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 配置解析器 - 简化版
 * <p>
 * 专门负责处理配置优先级逻辑：注解配置 > 统一配置管理器 > 默认值
 * <p>
 * 配置优先级说明：
 * 1. 注解配置 - 最高优先级，直接在注解中指定的值
 * 2. 统一配置管理器 - 中等优先级，从配置文件中读取的值
 * 3. 默认值 - 最低优先级，框架内置的默认值
 * <p>
 * 重要变更：
 * - 移除了对 TiprayTransactionProperties 的直接依赖
 * - 所有配置获取都通过 TransactionConfigurationManager 进行
 * - 保持配置优先级逻辑不变
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-20
 */
@Slf4j
public class ConfigurationResolver {

    @Autowired
    private TransactionConfigurationManager configManager;

    // ==================== 超时配置解析 ====================

    /**
     * 解析超时时间
     * 优先级：注解值 > 统一配置 > 默认值
     *
     * @param annotationValue 注解中的值
     * @param configKey       配置键（已废弃，保留兼容性）
     * @param defaultValue    默认值
     * @return 最终的超时时间（毫秒）
     */
    public long resolveTimeout(long annotationValue, String configKey, long defaultValue) {
        // 1. 如果注解中指定了值且不是默认值，使用注解值
        if (annotationValue > 0 && annotationValue != getAnnotationDefaultTimeout()) {
            log.debug("使用注解配置的超时时间: {}ms", annotationValue);
            return annotationValue;
        }

        // 2. 使用AT模式默认超时配置（已经是毫秒）
        long globalConfig = configManager.getAtDefaultTimeout();
        if (globalConfig > 0) {
            log.debug("使用全局配置的超时时间: {}ms", globalConfig);
            return globalConfig;
        }

        // 3. 使用默认值
        log.debug("使用默认超时时间: {}ms", defaultValue);
        return defaultValue;
    }

    /**
     * 解析连接超时时间
     */
    public long resolveConnectTimeout(long annotationValue, String serviceName) {
        // 获取默认连接超时时间配置
        long defaultConnectTimeout = configManager.getAtDefaultConnectTimeout();

        // 1. 注解值（最高优先级）
        if (annotationValue > 0 && annotationValue != defaultConnectTimeout) {
            log.debug("使用注解配置的连接超时时间: {}ms", annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        // TODO: 这里可以扩展为从Properties中获取服务特定配置

        // 3. 全局配置（低优先级）
        if (defaultConnectTimeout > 0) {
            log.debug("使用全局配置的连接超时时间: {}ms", defaultConnectTimeout);
            return defaultConnectTimeout;
        }

        // 4. 框架默认值（最低优先级）
        long frameworkDefault = 5000L;
        log.debug("使用框架默认连接超时时间: {}ms", frameworkDefault);
        return frameworkDefault;
    }

    /**
     * 解析读取超时时间
     */
    public long resolveReadTimeout(long annotationValue, String serviceName) {
        // 获取默认读取超时时间配置
        long defaultReadTimeout = configManager.getAtDefaultReadTimeout();

        // 1. 注解值（最高优先级）
        if (annotationValue > 0 && annotationValue != defaultReadTimeout) {
            log.debug("使用注解配置的读取超时时间: {}ms", annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        // TODO: 这里可以扩展为从Properties中获取服务特定配置

        // 3. 全局配置（低优先级）
        if (defaultReadTimeout > 0) {
            log.debug("使用全局配置的读取超时时间: {}ms", defaultReadTimeout);
            return defaultReadTimeout;
        }

        // 4. 框架默认值（最低优先级）
        long frameworkDefault = 30000L;
        log.debug("使用框架默认读取超时时间: {}ms", frameworkDefault);
        return frameworkDefault;
    }

    // ==================== 重试配置解析 ====================

    /**
     * 解析重试次数
     */
    public int resolveRetryCount(int annotationValue, String serviceName) {
        // 1. 注解值（最高优先级）
        if (annotationValue >= 0 && annotationValue != 0) { // 0是注解默认值
            log.debug("使用注解配置的重试次数: {}", annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        // TODO: 这里可以扩展为从Properties中获取服务特定配置

        // 3. 全局配置（低优先级）
        int globalConfig = configManager.getDefaultRetryCount();
        if (globalConfig >= 0) {
            log.debug("使用全局配置的重试次数: {}", globalConfig);
            return globalConfig;
        }

        // 4. 默认值（最低优先级）
        log.debug("使用默认重试次数: 3");
        return 3;
    }

    /**
     * 解析重试间隔
     */
    public long resolveRetryInterval(long annotationValue, String serviceName) {
        // 获取默认重试间隔配置
        long defaultRetryInterval = configManager.getDefaultRetryInterval();

        // 1. 注解值（最高优先级）
        if (annotationValue > 0 && annotationValue != defaultRetryInterval) {
            log.debug("使用注解配置的重试间隔: {}ms", annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        // TODO: 这里可以扩展为从Properties中获取服务特定配置

        // 3. 全局配置（低优先级）
        if (defaultRetryInterval > 0) {
            log.debug("使用全局配置的重试间隔: {}ms", defaultRetryInterval);
            return defaultRetryInterval;
        }

        // 4. 框架默认值（最低优先级）
        long frameworkDefault = 1000L;
        log.debug("使用框架默认重试间隔: {}ms", frameworkDefault);
        return frameworkDefault;
    }

    // ==================== 线程池配置解析 ====================

    /**
     * 解析核心线程数
     */
    public int resolveCorePoolSize(String poolName) {
        // 根据线程池名称获取特定配置
        TiprayTransactionProperties.PoolConfig poolConfig = getPoolConfig(poolName);
        if (poolConfig != null && poolConfig.getCoreSize() > 0) {
            log.debug("使用{}线程池特定配置的核心线程数: {}", poolName, poolConfig.getCoreSize());
            return poolConfig.getCoreSize();
        }

        // 使用全局默认配置
        int defaultCoreSize = configManager.getThreadPoolDefaultCoreSize();
        log.debug("使用全局默认核心线程数: {}", defaultCoreSize);
        return defaultCoreSize;
    }

    /**
     * 解析最大线程数
     */
    public int resolveMaximumPoolSize(String poolName) {
        // 根据线程池名称获取特定配置
        TiprayTransactionProperties.PoolConfig poolConfig = getPoolConfig(poolName);
        if (poolConfig != null && poolConfig.getMaxSize() > 0) {
            log.debug("使用{}线程池特定配置的最大线程数: {}", poolName, poolConfig.getMaxSize());
            return poolConfig.getMaxSize();
        }

        // 使用全局默认配置
        int defaultMaxSize = configManager.getThreadPoolDefaultMaxSize();
        log.debug("使用全局默认最大线程数: {}", defaultMaxSize);
        return defaultMaxSize;
    }

    /**
     * 解析队列容量
     */
    public int resolveQueueCapacity(String poolName) {
        // 根据线程池名称获取特定配置
        TiprayTransactionProperties.PoolConfig poolConfig = getPoolConfig(poolName);
        if (poolConfig != null && poolConfig.getQueueCapacity() > 0) {
            log.debug("使用{}线程池特定配置的队列容量: {}", poolName, poolConfig.getQueueCapacity());
            return poolConfig.getQueueCapacity();
        }

        // 使用全局默认配置
        int defaultQueueCapacity = configManager.getThreadPoolDefaultQueueCapacity();
        log.debug("使用全局默认队列容量: {}", defaultQueueCapacity);
        return defaultQueueCapacity;
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取注解默认超时时间
     * 这个值应该与注解中的默认值保持一致
     */
    private long getAnnotationDefaultTimeout() {
        // 从配置管理器获取默认超时时间，如果没有配置则使用框架默认值（已经是毫秒）
        long configuredTimeout = configManager.getAtDefaultTimeout();
        return configuredTimeout > 0 ? configuredTimeout : 30000L; // 框架默认值
    }

    /**
     * 解析布尔配置（已废弃，保留兼容性）
     */
    @Deprecated
    public boolean resolveBoolean(String configKey, boolean defaultValue) {
        return defaultValue;
    }

    /**
     * 解析字符串配置（已废弃，保留兼容性）
     */
    @Deprecated
    public String resolveString(String configKey, String defaultValue) {
        return defaultValue;
    }

    /**
     * 解析整数配置（已废弃，保留兼容性）
     */
    @Deprecated
    public int resolveInt(String configKey, int defaultValue) {
        return defaultValue;
    }

    /**
     * 构建服务特定的配置键
     */
    public String buildServiceConfigKey(String serviceName, String configName) {
        return "service." + serviceName + "." + configName;
    }

    /**
     * 构建全局配置键
     */
    public String buildGlobalConfigKey(String configName) {
        return "default-" + configName;
    }

    /**
     * 根据线程池名称获取特定配置
     */
    private TiprayTransactionProperties.PoolConfig getPoolConfig(String poolName) {
        switch (poolName.toLowerCase()) {
            case "retry":
                return configManager.getRetryThreadPoolConfig();
            case "compensation":
                return configManager.getCompensationThreadPoolConfig();
            case "scheduled":
                return configManager.getScheduledThreadPoolConfig();
            default:
                return null;
        }
    }

    // ==================== 新增配置解析方法 ====================

    /**
     * 解析服务特定的连接超时时间
     */
    public long resolveServiceConnectTimeout(long annotationValue, String serviceName) {
        // 获取默认连接超时时间配置
        long defaultConnectTimeout = configManager.getAtDefaultConnectTimeout();

        // 1. 注解值（最高优先级）
        if (annotationValue > 0 && annotationValue != defaultConnectTimeout) {
            log.debug("使用注解配置的服务{}连接超时时间: {}ms", serviceName, annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        TiprayTransactionProperties.ServiceMappingConfig serviceConfig = configManager.getServiceMappingConfig(serviceName);
        if (serviceConfig != null && serviceConfig.getConnectTimeout() > 0) {
            log.debug("使用服务{}特定配置的连接超时时间: {}ms", serviceName, serviceConfig.getConnectTimeout());
            return serviceConfig.getConnectTimeout();
        }

        // 3. 全局配置（低优先级）
        if (defaultConnectTimeout > 0) {
            log.debug("使用全局配置的连接超时时间: {}ms", defaultConnectTimeout);
            return defaultConnectTimeout;
        }

        // 4. 框架默认值（最低优先级）
        long frameworkDefault = 5000L;
        log.debug("使用框架默认连接超时时间: {}ms", frameworkDefault);
        return frameworkDefault;
    }

    /**
     * 解析服务特定的读取超时时间
     */
    public long resolveServiceReadTimeout(long annotationValue, String serviceName) {
        // 获取默认读取超时时间配置
        long defaultReadTimeout = configManager.getAtDefaultReadTimeout();

        // 1. 注解值（最高优先级）
        if (annotationValue > 0 && annotationValue != defaultReadTimeout) {
            log.debug("使用注解配置的服务{}读取超时时间: {}ms", serviceName, annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        TiprayTransactionProperties.ServiceMappingConfig serviceConfig = configManager.getServiceMappingConfig(serviceName);
        if (serviceConfig != null && serviceConfig.getReadTimeout() > 0) {
            log.debug("使用服务{}特定配置的读取超时时间: {}ms", serviceName, serviceConfig.getReadTimeout());
            return serviceConfig.getReadTimeout();
        }

        // 3. 全局配置（低优先级）
        if (defaultReadTimeout > 0) {
            log.debug("使用全局配置的读取超时时间: {}ms", defaultReadTimeout);
            return defaultReadTimeout;
        }

        // 4. 框架默认值（最低优先级）
        long frameworkDefault = 30000L;
        log.debug("使用框架默认读取超时时间: {}ms", frameworkDefault);
        return frameworkDefault;
    }

    /**
     * 解析服务特定的重试次数
     */
    public int resolveServiceRetryCount(int annotationValue, String serviceName) {
        // 获取默认重试次数配置
        int defaultRetryCount = configManager.getDefaultRetryCount();

        // 1. 注解值（最高优先级）
        if (annotationValue >= 0 && annotationValue != defaultRetryCount) {
            log.debug("使用注解配置的服务{}重试次数: {}", serviceName, annotationValue);
            return annotationValue;
        }

        // 2. 服务特定配置（中等优先级）
        TiprayTransactionProperties.ServiceMappingConfig serviceConfig = configManager.getServiceMappingConfig(serviceName);
        if (serviceConfig != null && serviceConfig.getRetryCount() >= 0) {
            log.debug("使用服务{}特定配置的重试次数: {}", serviceName, serviceConfig.getRetryCount());
            return serviceConfig.getRetryCount();
        }

        // 3. 全局配置（低优先级）
        if (defaultRetryCount >= 0) {
            log.debug("使用全局配置的重试次数: {}", defaultRetryCount);
            return defaultRetryCount;
        }

        // 4. 框架默认值（最低优先级）
        int frameworkDefault = 3;
        log.debug("使用框架默认重试次数: {}", frameworkDefault);
        return frameworkDefault;
    }

    /**
     * 解析配置是否启用
     */
    public boolean resolveEnabled(boolean annotationValue, boolean defaultValue) {
        // 注解值优先，然后是全局配置，最后是默认值
        if (annotationValue != defaultValue) {
            log.debug("使用注解配置的启用状态: {}", annotationValue);
            return annotationValue;
        }

        boolean globalEnabled = configManager.isEnabled();
        log.debug("使用全局配置的启用状态: {}", globalEnabled);
        return globalEnabled;
    }
}
