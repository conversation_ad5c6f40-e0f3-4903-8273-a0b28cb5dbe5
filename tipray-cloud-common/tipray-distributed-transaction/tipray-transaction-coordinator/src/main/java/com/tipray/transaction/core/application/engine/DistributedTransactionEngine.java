package com.tipray.transaction.core.application.engine;

import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import com.tipray.transaction.core.application.coordinator.BranchTransactionCoordinator;
import com.tipray.transaction.core.application.exception.TransactionExceptionHandler;
import com.tipray.transaction.core.application.extension.main.TransactionExtensionManager;
import com.tipray.transaction.core.application.propagation.TransactionPropagationManager;
import com.tipray.transaction.core.context.TransactionContextHolder;
import com.tipray.transaction.core.domain.funcation.TransactionCallback;
import com.tipray.transaction.core.domain.propagation.PropagationResult;
import com.tipray.transaction.core.domain.statemachine.main.TransactionStateMachine;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.BranchTransationConfig;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.domain.transaction.TransactionDefinition;
import com.tipray.transaction.core.enums.TransactionEvent;
import com.tipray.transaction.core.enums.TransactionMode;
import com.tipray.transaction.core.enums.TransactionStatus;
import com.tipray.transaction.core.exception.*;
import com.tipray.transaction.core.infrastructure.barrier.TransactionBarrier;
import com.tipray.transaction.core.infrastructure.monitor.TransactionTimeoutMonitor;
import com.tipray.transaction.core.util.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeoutException;

/**
 * 分布式事务引擎
 * 框架的核心组件，统一管理事务生命周期
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-23
 */
@Slf4j
public class DistributedTransactionEngine {

    // 核心功能组件
    private final TransactionStateMachine stateMachine;
    private final TransactionPropagationManager propagationManager;
    private final TransactionExtensionManager extensionManager;
    private final TransactionExceptionHandler exceptionHandler;

    private final BranchTransactionCoordinator branchCoordinator;
    private final TransactionBarrier transactionBarrier;

    @Autowired
    private TransactionTimeoutMonitor transactionTimeoutMonitor;


    public DistributedTransactionEngine(TransactionStateMachine stateMachine,
                                        TransactionPropagationManager propagationManager,
                                        TransactionExtensionManager extensionManager,
                                        TransactionExceptionHandler exceptionHandler,
                                        BranchTransactionCoordinator branchCoordinator,
                                        TransactionBarrier transactionBarrier) {
        this.stateMachine = stateMachine;
        this.propagationManager = propagationManager;
        this.extensionManager = extensionManager;
        this.exceptionHandler = exceptionHandler;
        this.branchCoordinator = branchCoordinator;
        this.transactionBarrier = transactionBarrier;
    }

    /**
     * 执行分布式事务
     * 统一的事务执行入口
     */
    public <T> T execute(TransactionDefinition definition, TransactionCallback<T> callback) {
        // 1. 处理事务传播
        PropagationResult propagationResult = propagationManager.handlePropagation(
                definition.toPropagationContext()
        );

        try {
            // 2. 开始事务
            TransactionContext context = beginTransaction(definition, propagationResult);

            // 3. 执行前置扩展
            extensionManager.executeBeforeTransaction(context);

            // 4. 执行业务逻辑
            T result = executeBusinessLogic(context, callback);

            // 5. 提交事务
            commitTransaction(context);

            // 6. 执行后置扩展
            extensionManager.executeAfterCommit(context);

            return result;
        } catch (Exception e) {
            // 7. 异常处理和回滚
            handleExceptionAndRollback(propagationResult, e);
            throw e;
        } finally {
            try {
                // 清理屏障信息
                cleanupTransactionBarrier();
                // 清理传播上下文 这边也有可能清理上下文
                propagationManager.cleanupPropagation(propagationResult);
            } finally {
                // 确保上下文清理在最后执行，避免影响事务回滚
                TransactionContextHolder.clearCurrentContext();
            }
        }
    }


    /**
     * 开始事务
     */
    private TransactionContext beginTransaction(TransactionDefinition definition,
                                                PropagationResult propagationResult) {

        if (propagationResult.isNewTransactionRequired()) {
            return doBeginTransaction(definition);
        } else {
            return propagationResult.getExistingContext();
        }
    }

    /**
     * 执行开始事务的核心逻辑
     */
    private TransactionContext doBeginTransaction(TransactionDefinition definition) {
        String transactionId = generateTransactionId();

        try {
            // 1. 显式初始化事务状态
            stateMachine.initializeTransactionStatus(transactionId, TransactionStatus.UNKNOWN);

            // 2. 创建事务上下文
            TransactionContext context = TransactionContext.builder()
                    .transactionId(transactionId)
                    .groupId(definition.getGroupId())
                    .mode(definition.getMode())
                    .propagation(definition.getPropagation())
                    .timeout(definition.getTimeout())
                    .readOnly(definition.isReadOnly())
                    .applicationName(definition.getApplicationName())
                    .methodSignature(definition.getMethodSignature())
                    .asyncCommitOrRollback(definition.isAsyncCommitOrRollback())
                    .enableBarrier(definition.isEnableBarrier())
                    .build();

            // 先设置一遍上下文
            TransactionContextHolder.setCurrentContext(context);

            // 3. 状态转换：UNKNOWN -> BEGIN（自动触发持久化和内存状态更新）
            stateMachine.transition(transactionId, TransactionEvent.START, context.toTransactionDO());

            // 3. 初始化分支事务协调（包含本地分支注册和本地事务开始）
            branchCoordinator.initialize(context);

            // 7. 设置事务上下文
            TransactionContextHolder.setCurrentContext(context);

            log.info("[{}|{}] [BEGIN] - 事务开始 {}",
                    LoggingUtils.getTxId(transactionId), null,
                    LoggingUtils.formatContext("mode", definition.getMode(), "method", definition.getApplicationName()));

            return context;

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 开始分布式事务失败 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatException(e), e);
            throw new DistributedTransactionBeginException("开始分布式事务失败", e);
        }
    }

    /**
     * 执行业务逻辑
     */
    private <T> T executeBusinessLogic(TransactionContext context,
                                       TransactionCallback<T> callback) {
        String transactionId = context.getTransactionId();

        // 状态转换：BEGIN -> EXECUTING（自动触发持久化和内存状态更新）
        stateMachine.transition(transactionId, TransactionEvent.EXECUTE);

        // 使用同步超时监控，保持Spring事务上下文
        return executeWithSyncTimeout(context, callback);
    }

    /**
     * 同步超时监控执行业务逻辑
     * 在当前线程执行，保持Spring事务上下文，通过中断机制实现超时控制
     */
    private <T> T executeWithSyncTimeout(TransactionContext context,
                                        TransactionCallback<T> callback) {
        String transactionId = context.getTransactionId();
        long timeoutMillis = context.getTimeout();
        Thread currentThread = Thread.currentThread();

        // 创建超时监控任务
        ScheduledFuture<?> timeoutTask = transactionTimeoutMonitor.scheduleTimeout(() -> {
            log.warn("[{}|{}] [TIMEOUT] - 事务执行超时，中断执行线程 {}",
                    LoggingUtils.getTxId(transactionId), null,
                    LoggingUtils.formatContext("timeout", timeoutMillis + "ms", "thread", currentThread.getName()));
            currentThread.interrupt();
        }, timeoutMillis);

        try {
            // 在当前线程执行业务逻辑，保持Spring事务上下文
            T result = callback.doInTransaction(context);

            // 成功完成，取消超时任务
            timeoutTask.cancel(false);

            return result;

        } catch (Exception e) {
            // 取消超时任务
            timeoutTask.cancel(false);

            // 检查是否是因为超时中断导致的异常
            if (Thread.interrupted() || isTimeoutRelated(e)) {
                log.error("[{}|{}] [ERROR] - 事务执行超时被中断 {}",
                        LoggingUtils.getTxId(transactionId), null,
                        LoggingUtils.formatContext("timeout", timeoutMillis + "ms", "cause", e.getClass().getSimpleName()));

                throw new DistributedTransactionTimeoutException(
                    "全局事务执行超时",
                    timeoutMillis,
                    System.currentTimeMillis(),
                    DistributedTransactionTimeoutException.TimeoutType.GLOBAL_TRANSACTION_TIMEOUT,
                    "执行全局事务"
                );
            }

            // 其他异常直接抛出
            throw e;
        }
    }

    /**
     * 判断异常是否与超时相关
     */
    private boolean isTimeoutRelated(Exception e) {
        if (e == null) return false;

        String message = e.getMessage();
        if (message == null) return false;

        // 检查常见的超时异常特征
        return message.contains("timeout") ||
               message.contains("超时") ||
               message.contains("interrupted") ||
               message.contains("中断") ||
               e instanceof java.sql.SQLTimeoutException ||
               e instanceof java.net.SocketTimeoutException ||
               e instanceof InterruptedException;
    }

    /**
     * 提交事务
     */
    private void commitTransaction(TransactionContext context) {
        String transactionId = context.getTransactionId();

        try {
            // 状态转换：EXECUTING -> COMMITTING（自动触发持久化和内存状态更新）
            stateMachine.transition(transactionId, TransactionEvent.COMMIT, context);

            // 统一提交所有分支（包括本地分支）
            branchCoordinator.commit(context);

            // 状态转换：COMMITTING -> COMMITTED（自动触发持久化和内存状态更新）
            stateMachine.transition(transactionId, TransactionEvent.COMMIT_SUCCESS, context);

            // 获取分支数量，自动计算总耗时
            int branchCount = branchCoordinator.getBranchCount(transactionId);
            log.info("[{}|{}] [COMMIT] - 事务提交成功 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatContext("branches", branchCount, "total", LoggingUtils.formatDuration(context.getStartTime())));

        } catch (Exception e) {
            log.error("[{}|{}] [ERROR] - 分布式事务提交失败 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatException(e), e);

            // 状态转换：COMMITTING -> COMMIT_FAILED（携带失败原因，自动更新内存状态）
            stateMachine.transitionWithFailure(transactionId, TransactionEvent.COMMIT_FAILURE,
                                             e, "事务提交失败: " + e.getMessage());

            throw new DistributedTransactionCommitException("分布式事务提交失败", e);
        }
    }

    /**
     * 异常处理和回滚
     */
    private void handleExceptionAndRollback(PropagationResult propagationResult, Exception e) {
        if (!propagationResult.isNewTransactionRequired()) {
            return;
        }

        TransactionContext context = TransactionContextHolder.getCurrentContext();
        if (context == null) {
            return;
        }

        String transactionId = context.getTransactionId();

        try {
            // 检查是否需要回滚
            if (exceptionHandler.shouldRollback(e, context.getMethodSignature())) {
                // 回滚前拓展
                extensionManager.executeBeforeRollback(context, e);

                // 执行回滚
                rollbackTransaction(context, e);

                // 回滚后拓展
                extensionManager.executeAfterRollback(context, e);
            } else {
                // 不需要回滚，仍然提交
                commitTransaction(context);
            }
        } catch (Exception rollbackException) {
            log.error("[{}|{}] [ERROR] - 事务回滚处理失败 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatException(rollbackException), rollbackException);

            // 将回滚异常转换为Tipray框架异常
            if (!(rollbackException instanceof DistributedTransactionException)) {
                throw new DistributedRollbackException(
                        "事务回滚处理失败: " + rollbackException.getMessage(),
                        rollbackException,
                        transactionId,
                        context.getGroupId(),
                        null,
                        DistributedRollbackException.RollbackFailureType.UNKNOWN
                );
            }
        }
    }

    /**
     * 回滚事务
     */
    private void rollbackTransaction(TransactionContext context, Exception cause) {
        String transactionId = context.getTransactionId();

        try {
            // 状态转换：* -> ROLLBACKING（携带回滚原因）
            Map<String, Object> contextData = new HashMap<>();
            contextData.put("originalException", cause);
            contextData.put("rollbackReason", "业务异常触发回滚");

            // 更新状态 - 回滚中
            stateMachine.transition(transactionId, TransactionEvent.ROLLBACK, context);

            // 统一回滚所有分支（包括本地分支）
            branchCoordinator.rollback(context, cause);

            // 状态转换：ROLLBACKING -> ROLLBACKED（自动触发持久化和内存状态更新）
            stateMachine.transition(transactionId, TransactionEvent.ROLLBACK_SUCCESS, context);

            log.info("[{}|{}] [ROLLBACK] - 事务回滚 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatContext("reason", "业务异常回滚", "cause", cause.getClass().getSimpleName()));

        } catch (Exception rollbackException) {
            log.error("[{}|{}] [ERROR] - 分布式事务回滚失败 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatException(rollbackException), rollbackException);

//            // 状态转换：ROLLBACKING -> ROLLBACK_FAILED（携带回滚失败原因）
//            Map<String, Object> failureData = new HashMap<>();
//            failureData.put("originalException", cause);
//            failureData.put("rollbackException", rollbackException);
//            failureData.put("failureTime", System.currentTimeMillis());

            stateMachine.transitionWithFailure(transactionId, TransactionEvent.ROLLBACK_FAILURE,
                                             rollbackException, "回滚失败: " + rollbackException.getMessage());

            // 回滚失败不抛异常，避免掩盖原始异常
        }
    }

    /**
     * 生成事务ID
     */
    private String generateTransactionId() {
        return "tx-" + System.currentTimeMillis() + "-" +
                ThreadLocalRandom.current().nextInt(10000);
    }

    /**
     * 获取当前事务上下文
     */
    public TransactionContext getCurrentContext() {
        return TransactionContextHolder.getCurrentContext();
    }

    /**
     * 获取事务状态
     */
    public TransactionStatus getTransactionStatus(String transactionId) {
        return stateMachine.getCurrentStatus(transactionId);
    }

    /**
     * 暂停事务
     */
    public void pauseTransaction(String transactionId, String reason) {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        if (context != null && context.getTransactionId().equals(transactionId)) {
            context.suspend(reason);
            stateMachine.transition(transactionId, TransactionEvent.PAUSE);
        }
    }

    /**
     * 恢复事务
     */
    public void resumeTransaction(String transactionId) {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        if (context != null && context.getTransactionId().equals(transactionId)) {
            context.resume();
            stateMachine.transition(transactionId, TransactionEvent.RESUME);
        }
    }

    /**
     * 强制回滚事务（用于人工干预）
     */
    public void forceRollback(String transactionId, String reason, String operator) {
        TransactionContext context = TransactionContextHolder.getCurrentContext();
        if (context != null && context.getTransactionId().equals(transactionId)) {
            log.warn("[{}|{}] [WARN] - 强制回滚事务 {}",
                    LoggingUtils.getTxId(), null,
                    LoggingUtils.formatContext("操作人", operator, "原因", reason));

            try {
                rollbackTransaction(context, new RuntimeException("强制回滚: " + reason));
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 强制回滚失败 {}",
                        LoggingUtils.getTxId(), null,
                        LoggingUtils.formatException(e), e);
            }
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 解析分支事务的配置
     *
     * @param annotation 分支事务的注解
     * @param method     目标方法
     * @param mode       事务模式
     */
    public BranchTransationConfig parseBranchTransationConfig(DistributedBranchTransaction annotation, Method method, TransactionMode mode) {
        return branchCoordinator.parseBranchTransationConfig(annotation, method, mode);
    }

    /**
     * 执行分支事务
     *
     * @param branchTransactionDO 分支事务
     * @param joinPoint           分支事务的目标方法
     * @return
     */
    public Object executeBranch(BranchTransactionDO branchTransactionDO, ProceedingJoinPoint joinPoint) throws TimeoutException {
        TransactionContext currentContext = getCurrentContext();

        if (currentContext == null) {
            throw new IllegalStateException("当前没有活跃的分布式事务");
        }

        // 这边执行分支注册
        branchCoordinator.registerBranch(currentContext, branchTransactionDO);

        // 分支执行整合超时监控
        return transactionTimeoutMonitor.executeWithTimeoutSafe(() ->
                branchCoordinator.executeBranch(currentContext, branchTransactionDO, joinPoint),
                branchTransactionDO.getTimeoutMillis(),
                "执行分支事务");
    }

    /**
     * 清理分支资源
     */
    public void clearBranchTransactionResource() {
        branchCoordinator.clearBranchTransactionResource(getCurrentContext().getMode());
    }

    /**
     * 清理事务屏障记录
     * 在事务完成（成功或失败）后调用
     */
    private void cleanupTransactionBarrier() {
        TransactionContext context = getCurrentContext();
        if (context.isEnableBarrier()) {
            try {
                transactionBarrier.cleanupBarrier(context.getTransactionId());
                log.debug("[{}|{}] [DEBUG] - 事务屏障记录清理完成",
                        LoggingUtils.getTxId(), null);
            } catch (Exception e) {
                log.warn("[{}|{}] [WARN] - 事务屏障记录清理失败 {}",
                        LoggingUtils.getTxId(), null,
                        LoggingUtils.formatContext("reason", e.getMessage()));
                // 屏障清理失败不影响主流程
            }
        }
    }

}
