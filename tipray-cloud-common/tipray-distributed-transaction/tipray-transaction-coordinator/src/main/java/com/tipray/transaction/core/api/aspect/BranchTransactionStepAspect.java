package com.tipray.transaction.core.api.aspect;

import com.tipray.transaction.core.annotation.DistributedBranchTransaction;
import com.tipray.transaction.core.application.engine.DistributedTransactionEngine;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.BranchTransationConfig;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 统一事务步骤切面
 * 合并了原有的AtServiceAspect和TransactionStepAspect功能
 * 根据外层@DistributedTransaction的模式自动适配AT或Saga模式处理逻辑
 * <p>
 * 核心功能：
 * 1. 统一的步骤执行流程管理
 * 2. 智能的模式适配（AT/Saga）
 * 3. 完善的重试和超时机制
 * 4. 统一的异常处理和状态管理
 * 5. 与DistributedTransactionManager深度集成
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
@Aspect
@Order(200) // 确保在DistributedTransactionAspect之后执行
@Slf4j
public class BranchTransactionStepAspect {

    private final DistributedTransactionEngine transactionEngine;

    public BranchTransactionStepAspect(DistributedTransactionEngine transactionEngine) {
        this.transactionEngine = transactionEngine;
    }

    /**
     * 切点：拦截所有@TransactionStep注解的方法
     */
    @Pointcut("@annotation(com.tipray.transaction.core.annotation.DistributedBranchTransaction)")
    public void transactionStepPointcut() {
    }

    /**
     * 环绕通知：统一处理事务步骤逻辑
     * 根据当前事务模式自动适配AT或Saga模式的处理逻辑
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 执行异常
     */
    @Around("transactionStepPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        DistributedBranchTransaction annotation = method.getAnnotation(DistributedBranchTransaction.class);

        // 检查是否在分布式事务中
        if (transactionEngine.getCurrentContext() == null) {
            return joinPoint.proceed();
        }

        // 获取当前事务上下文
        TransactionContext context = transactionEngine.getCurrentContext();
        if (context == null) {
            return joinPoint.proceed();
        }

        // 解析步骤配置，校验每个事务模式的必填项
        BranchTransationConfig config = transactionEngine.parseBranchTransationConfig(annotation, method, context.getMode());

        // 创建事务步骤
        BranchTransactionDO branchTransaction = createBranchTransaction(config, context);

        // 准备执行步骤
        try {
            // 使用新的DistributedTransactionEngine执行步骤
            return executeStepWithEngine(branchTransaction, joinPoint);
        } finally {
            // 清理资源
            transactionEngine.clearBranchTransactionResource();
        }
    }

    /**
     * 使用新的事务引擎执行步骤
     */
    private Object executeStepWithEngine(BranchTransactionDO branchTransactionDO,
                                         ProceedingJoinPoint joinPoint) throws Throwable {
        // 通过引擎执行分支
        return transactionEngine.executeBranch(branchTransactionDO, joinPoint);
    }

    /**
     * 创建事务步骤
     */
    private BranchTransactionDO createBranchTransaction(
            BranchTransationConfig config, TransactionContext context) {

        // 使用静态工厂方法创建步骤
        BranchTransactionDO branchTransactionDO =
                BranchTransactionDO.create(
                        config.getStepName(),
                        config.getTargetService(),
                        "executeStep" // TODO默认方法名
                );

        branchTransactionDO.setTransactionId(context.getTransactionId());
        branchTransactionDO.setCompensationMethod(config.getCompensation());
        branchTransactionDO.setCritical(config.isCritical());
        branchTransactionDO.setRetryCount(config.getRetryCount());
        branchTransactionDO.setTimeoutMillis(config.getTimeout()); // 直接使用毫秒

        // 从事务上下文中获取enableBarrier设置
        branchTransactionDO.setEnableBarrier(context.isEnableBarrier());

        return branchTransactionDO;
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取TransactionStep注解
     */
    private DistributedBranchTransaction getTransactionStepAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(DistributedBranchTransaction.class);
    }

    /**
     * 获取步骤名称
     */
    private String getStepName(DistributedBranchTransaction annotation, ProceedingJoinPoint joinPoint) {
        if (annotation.value() != null && !annotation.value().trim().isEmpty()) {
            return annotation.value();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return signature.getMethod().getName();
    }

}
