package com.tipray.transaction.core.infrastructure.retry;

import cn.hutool.core.util.ObjectUtil;
import com.tipray.transaction.core.config.TransactionConfigurationManager;
import com.tipray.transaction.core.domain.transaction.BranchTransactionDO;
import com.tipray.transaction.core.domain.transaction.TransactionContext;
import com.tipray.transaction.core.exception.DistributedTransactionSystemException;
import lombok.extern.slf4j.Slf4j;
import com.tipray.transaction.core.util.LoggingUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

/**
 * 重试管理器 - 简洁优雅版
 *
 * 设计特点：
 * 1. 支持 RetryConfig 配置和默认配置两种模式
 * 2. 纯同步重试，简单可靠
 * 3. 智能延迟策略：指数退避 + 随机抖动
 * 4. 异常分类：区分可重试和不可重试异常
 * 5. 优雅的日志记录，避免刷屏
 *
 * <AUTHOR>
 * @version 3.0
 * @since 2025-01-23
 */
@Slf4j
public class RetryManager {

    @Autowired
    private TransactionConfigurationManager configManager;

    @PostConstruct
    public void init() {
        log.info("重试管理器初始化完成 - 支持配置驱动的智能重试策略");
    }

    // ==================== 使用 RetryConfig 的重试方法 ====================

    /**
     * 使用 RetryConfig 执行重试操作
     *
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param retryConfig 重试配置
     * @return 操作结果
     * @throws RuntimeException 重试次数用尽后抛出异常
     */
    public <T> T executeWithRetry(Callable<T> operation, String operationName, RetryConfig retryConfig) {
        if (retryConfig == null) {
            throw new IllegalArgumentException("重试配置不能为空");
        }

        retryConfig.validate();

        // 根据 retryConfig 的配置来决定执行策略
        return executeWithRetryInternal(operation, operationName, retryConfig);
    }

    /**
     * 使用 RetryConfig 执行 Runnable 重试操作
     */
    public void executeWithRetry(Runnable operation, String operationName, RetryConfig retryConfig) {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, operationName, retryConfig);
    }

    /**
     * 业务重试执行（保持原始异常）
     * 用于业务逻辑的重试，重试耗尽时抛出原始异常，不转换为框架异常
     */
    public <T> T executeBusinessWithRetry(Callable<T> operation, String operationName, RetryConfig retryConfig) {
        if (retryConfig == null) {
            throw new IllegalArgumentException("重试配置不能为空");
        }

        if (ObjectUtil.isEmpty(retryConfig.getRetryInterval()) || Objects.equals(retryConfig.getRetryInterval(), 0)) {
            long retryInterval = configManager.getDefaultRetryInitialInterval();
            retryConfig.setRetryInterval(retryInterval);
        }

        if (ObjectUtil.isEmpty(retryConfig.getBackoffMultiplier()) || Objects.equals(retryConfig.getBackoffMultiplier(), 0)) {
            double backoffMultiplier = configManager.getDefaultRetryBackoffMultiplier();
            retryConfig.setBackoffMultiplier(backoffMultiplier);
        }

        retryConfig.validate();

        // 执行业务重试，保持原始异常
        return executeBusinessWithRetryInternal(operation, operationName, retryConfig);
    }

    /**
     * 分支事务重试执行（使用 RetryConfig）
     * 用于事务框架内部的重试
     */
    public <T> T executeBranchWithRetry(TransactionContext context, BranchTransactionDO branchTransaction,
                                       Supplier<T> action, RetryConfig retryConfig) {
        String transactionId = context.getTransactionId();
        String stepName = branchTransaction.getStepName();
        String operationName = String.format("[%s] 步骤[%s]", transactionId, stepName);

        return executeWithRetry(action::get, operationName, retryConfig);
    }

    // ==================== 使用默认配置的重试方法 ====================

    /**
     * 使用默认配置执行重试操作
     *
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param maxAttempts 最大尝试次数（包括首次执行）
     * @return 操作结果
     * @throws RuntimeException 重试次数用尽后抛出异常
     */
    public <T> T executeWithRetry(Callable<T> operation, String operationName, int maxAttempts) {
        if (maxAttempts <= 0) {
            throw new IllegalArgumentException("最大尝试次数必须大于0");
        }

        // 创建默认重试配置
        RetryConfig defaultConfig = createDefaultRetryConfig(maxAttempts - 1); // maxAttempts包括首次执行
        return executeWithRetryInternal(operation, operationName, defaultConfig);
    }

    /**
     * 使用默认配置执行重试操作（使用全局默认重试次数）
     */
    public <T> T executeWithRetry(Callable<T> operation, String operationName) {
        int defaultRetryCount = configManager.getDefaultRetryCount();
        return executeWithRetry(operation, operationName, defaultRetryCount + 1); // +1 包括首次执行
    }

    /**
     * 使用默认配置执行 Runnable 重试操作
     */
    public void executeWithRetry(Runnable operation, String operationName, int maxAttempts) {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, operationName, maxAttempts);
    }

    /**
     * 使用默认配置执行 Runnable 重试操作（使用全局默认重试次数）
     */
    public void executeWithRetry(Runnable operation, String operationName) {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, operationName);
    }

    /**
     * 分支事务重试执行（使用默认配置）
     */
    public <T> T executeBranchWithRetry(TransactionContext context, BranchTransactionDO branchTransaction,
                                       Supplier<T> action) {
        String transactionId = context.getTransactionId();
        String stepName = branchTransaction.getStepName();
        String operationName = String.format("[%s] 步骤[%s]", transactionId, stepName);

        // 优先使用分支事务配置的重试次数
        int maxAttempts;
        if (branchTransaction.getRetryCount() != null && branchTransaction.getRetryCount() > 0) {
            maxAttempts = branchTransaction.getRetryCount() + 1; // +1 包括首次执行
        } else {
            maxAttempts = configManager.getDefaultRetryCount() + 1;
        }

        return executeWithRetry(action::get, operationName, maxAttempts);
    }

    // ==================== 内部实现方法 ====================

    /**
     * 重试执行的内部实现
     */
    private <T> T executeWithRetryInternal(Callable<T> operation, String operationName, RetryConfig retryConfig) {
        // 检查重试是否启用
        if (!retryConfig.isEnabled()) {
            // 重试未启用，直接执行一次
            try {
                T result = operation.call();
                log.debug("操作执行成功（重试未启用）: {}", operationName);
                return result;
            } catch (Exception e) {
                log.error("操作执行失败（重试未启用）: {} - {}", operationName, getSimpleErrorMessage(e));
                throw new DistributedTransactionSystemException(
                        "操作执行失败: " + operationName,
                        e,
                        DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                        DistributedTransactionSystemException.SeverityLevel.MEDIUM,
                        operationName
                );
            }
        }

        // 检查重试策略
        if (retryConfig.getStrategy() == RetryConfig.RetryStrategy.NO_RETRY || retryConfig.getMaxRetries() <= 0) {
            // 策略为不重试或重试次数为0，直接执行一次
            try {
                T result = operation.call();
                log.debug("操作执行成功（不重试策略）: {}", operationName);
                return result;
            } catch (Exception e) {
                log.error("操作执行失败（不重试策略）: {} - {}", operationName, getSimpleErrorMessage(e));
                throw new DistributedTransactionSystemException(
                        "操作执行失败: " + operationName,
                        e,
                        DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                        DistributedTransactionSystemException.SeverityLevel.MEDIUM,
                        operationName
                );
            }
        }

        // 执行带重试的操作
        Exception lastException = null;
        int totalAttempts = retryConfig.getMaxRetries() + 1; // +1 包括首次执行

        for (int attempt = 1; attempt <= totalAttempts; attempt++) {
            try {
                T result = operation.call();

                // 成功时的日志
                if (attempt > 1) {
                    log.info("[{}|{}] [EXEC] - 重试成功 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName, "attempt", attempt));
                } else {
                    log.debug("[{}|{}] [EXEC] - 操作执行成功 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName));
                }

                return result;

            } catch (Exception e) {
                lastException = e;

                // 最后一次尝试失败
                if (attempt == totalAttempts) {
                    log.error("[{}|{}] [ERROR] - 重试最终失败 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName, "attempts", attempt, "error", getSimpleErrorMessage(e)));
                    break;
                }

                // 检查是否应该重试
                if (!shouldRetry(e, retryConfig)) {
                    log.warn("[{}|{}] [WARN] - 遇到不可重试异常，停止重试 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName, "attempt", attempt, "error", getSimpleErrorMessage(e)));
                    break;
                }

                // 计算延迟并等待
                long delay = calculateDelay(attempt, retryConfig);
                log.warn("[{}|{}] [WARN] - 重试失败，{}ms后重试 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        delay,
                        LoggingUtils.formatContext("operation", operationName, "attempt", attempt, "error", getSimpleErrorMessage(e)));

                sleepQuietly(delay);
            }
        }

        throw new DistributedTransactionSystemException(
                "重试次数已用尽: " + operationName,
                lastException,
                DistributedTransactionSystemException.SystemExceptionType.UNKNOWN,
                DistributedTransactionSystemException.SeverityLevel.HIGH,
                operationName
        );
    }

    /**
     * 业务重试的内部实现
     * 重试耗尽时抛出原始异常，不转换为框架异常
     */
    private <T> T executeBusinessWithRetryInternal(Callable<T> operation, String operationName, RetryConfig retryConfig) {
        // 检查重试是否启用
        if (!retryConfig.isEnabled()) {
            // 重试未启用，直接执行一次
            try {
                T result = operation.call();
                log.debug("[{}|{}] [EXEC] - 业务操作执行成功（重试未启用） {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("operation", operationName));
                return result;
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 业务操作执行失败（重试未启用） {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("operation", operationName, "error", getSimpleErrorMessage(e)));
                // 业务异常直接抛出原始异常
                throw new RuntimeException(e);
            }
        }

        // 检查重试策略
        if (retryConfig.getStrategy() == RetryConfig.RetryStrategy.NO_RETRY || retryConfig.getMaxRetries() <= 0) {
            // 策略为不重试或重试次数为0，直接执行一次
            try {
                T result = operation.call();
                log.debug("[{}|{}] [EXEC] - 业务操作执行成功（不重试策略） {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("operation", operationName));
                return result;
            } catch (Exception e) {
                log.error("[{}|{}] [ERROR] - 业务操作执行失败（不重试策略） {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        LoggingUtils.formatContext("operation", operationName, "error", getSimpleErrorMessage(e)));
                // 业务异常直接抛出原始异常
                throw new RuntimeException(e);
            }
        }

        // 执行带重试的业务操作
        Exception lastException = null;
        int totalAttempts = retryConfig.getMaxRetries() + 1; // +1 包括首次执行

        for (int attempt = 1; attempt <= totalAttempts; attempt++) {
            try {
                T result = operation.call();

                // 成功时的日志
                if (attempt > 1) {
                    log.info("[{}|{}] [EXEC] - 业务重试成功 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName, "attempt", attempt));
                } else {
                    log.debug("[{}|{}] [EXEC] - 业务操作执行成功 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName));
                }

                return result;

            } catch (Exception e) {
                lastException = e;

                // 最后一次尝试失败
                if (attempt == totalAttempts) {
                    log.error("[{}|{}] [ERROR] - 业务重试最终失败 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName, "attempts", attempt, "error", getSimpleErrorMessage(e)));
                    break;
                }

                // 检查是否应该重试
                if (!shouldRetry(e, retryConfig)) {
                    log.warn("[{}|{}] [WARN] - 业务遇到不可重试异常，停止重试 {}",
                            LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                            LoggingUtils.formatContext("operation", operationName, "attempt", attempt, "error", getSimpleErrorMessage(e)));
                    break;
                }

                // 计算延迟并等待
                long delay = calculateDelay(attempt, retryConfig);
                log.warn("[{}|{}] [WARN] - 业务重试失败，{}ms后重试 {}",
                        LoggingUtils.getTxId(), LoggingUtils.getBranchId(),
                        delay,
                        LoggingUtils.formatContext("operation", operationName, "attempt", attempt, "error", getSimpleErrorMessage(e)));

                sleepQuietly(delay);
            }
        }

        // 业务重试耗尽时，抛出原始异常，不转换为框架异常
        log.error("业务重试次数已用尽，抛出原始异常: {} - {}", operationName, getSimpleErrorMessage(lastException));
        throw new RuntimeException(lastException);
    }

    /**
     * 创建默认重试配置
     */
    private RetryConfig createDefaultRetryConfig(int maxRetries) {
        return RetryConfig.builder()
                .maxRetries(maxRetries)
                .retryInterval(configManager.getDefaultRetryInterval())
                .backoffMultiplier(configManager.getDefaultRetryBackoffMultiplier())
                .maxRetryInterval(configManager.getDefaultRetryMaxInterval())
                .enabled(true)
                .strategy(RetryConfig.RetryStrategy.EXPONENTIAL_BACKOFF)
                .build();
    }

    /**
     * 安静地睡眠，处理中断异常
     */
    private void sleepQuietly(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("重试被中断", e);
        }
    }

    /**
     * 判断异常是否应该重试
     */
    private boolean shouldRetry(Exception e, RetryConfig retryConfig) {
        // 使用 RetryConfig 的异常判断逻辑
        if (!retryConfig.shouldRetry(e)) {
            return false;
        }

        // 补充框架级别的异常判断
        return isRetryableException(e);
    }

    /**
     * 框架级别的异常重试判断
     */
    private boolean isRetryableException(Exception e) {
        // 网络相关异常可以重试
        if (e instanceof java.net.ConnectException ||
                e instanceof java.net.SocketTimeoutException ||
                e instanceof java.net.UnknownHostException ||
                e instanceof java.net.SocketException) {
            return true;
        }

        // 超时异常可以重试
        if (e instanceof java.util.concurrent.TimeoutException) {
            return true;
        }

        // HTTP相关异常可以重试
        String message = e.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("timeout") ||
                lowerMessage.contains("connection") ||
                lowerMessage.contains("network") ||
                lowerMessage.contains("unavailable") ||
                lowerMessage.contains("refused") ||
                lowerMessage.contains("reset") ||
                lowerMessage.contains("502") ||
                lowerMessage.contains("503") ||
                lowerMessage.contains("504")) {
                return true;
            }
        }

        // 业务异常不重试
        if (e instanceof IllegalArgumentException ||
                e instanceof IllegalStateException ||
                e instanceof NullPointerException ||
                e instanceof SecurityException) {
            return false;
        }

        // 其他RuntimeException可以重试
        return e instanceof RuntimeException;
    }

    /**
     * 计算延迟时间
     */
    private long calculateDelay(int attempt, RetryConfig retryConfig) {
        long baseInterval = retryConfig.getRetryInterval();
        double backoffMultiplier = retryConfig.getBackoffMultiplier();
        long maxInterval = retryConfig.getMaxRetryInterval();

        long delay;
        switch (retryConfig.getStrategy()) {
            case FIXED_INTERVAL:
                delay = baseInterval;
                break;
            case LINEAR_BACKOFF:
                delay = baseInterval * attempt;
                break;
            case EXPONENTIAL_BACKOFF:
            default:
                delay = (long) (baseInterval * Math.pow(backoffMultiplier, attempt - 1));
                break;
        }

        // 限制最大延迟
        delay = Math.min(delay, maxInterval);

        // 添加随机抖动（如果启用）
        if (retryConfig.isEnableJitter()) {
            double jitterRange = retryConfig.getJitterRange();
            double jitter = 1.0 - jitterRange + (ThreadLocalRandom.current().nextDouble() * jitterRange * 2);
            delay = (long) (delay * jitter);
        }

        // 确保最小延迟
        return Math.max(delay, 100L);
    }

    /**
     * 获取简化的错误信息，避免日志刷屏
     */
    private String getSimpleErrorMessage(Throwable throwable) {
        if (throwable == null) {
            return "未知错误";
        }

        String message = throwable.getMessage();
        if (message == null || message.trim().isEmpty()) {
            return throwable.getClass().getSimpleName();
        }

        // 限制错误信息长度，避免过长的日志
        if (message.length() > 200) {
            return message.substring(0, 200) + "...";
        }

        return message;
    }
}
